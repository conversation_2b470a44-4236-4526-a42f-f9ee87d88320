const axios = require('axios');

async function testFrontendRegistration() {
  try {
    console.log('Testing frontend registration...');
    
    // Test direct API call to backend
    console.log('1. Testing direct backend API call...');
    const backendResponse = await axios.post('http://localhost:3001/api/auth/register', {
      email: `backend${Date.now()}@example.com`,
      password: 'Test123!',
      name: 'Backend Test User'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('✅ Backend registration successful!');
    console.log('Backend Response Status:', backendResponse.status);
    
    // Test frontend API route (if it exists)
    console.log('\n2. Testing frontend API route...');
    try {
      const frontendResponse = await axios.post('http://localhost:3000/api/auth/register', {
        email: `frontend${Date.now()}@example.com`,
        password: 'Test123!',
        name: 'Frontend Test User'
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log('✅ Frontend API route registration successful!');
      console.log('Frontend Response Status:', frontendResponse.status);
    } catch (frontendError) {
      console.log('❌ Frontend API route failed:');
      if (frontendError.response) {
        console.log('Status:', frontendError.response.status);
        console.log('Data:', JSON.stringify(frontendError.response.data, null, 2));
      } else {
        console.log('Error:', frontendError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Backend registration failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

testFrontendRegistration();
