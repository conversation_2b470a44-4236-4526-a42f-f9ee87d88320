"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhancedAIProvider = exports.EnhancedAIProvider = void 0;
const logger_1 = require("./logger");
class EnhancedAIProvider {
    constructor() {
        this.providers = new Map();
        this.taskConfigs = new Map();
        this.initializeProviders();
    }
    initializeProviders() {
        // Initialize with default providers
        logger_1.logger.info('Initializing Enhanced AI Provider...');
    }
    async addProvider(config) {
        this.providers.set(config.id, config);
        logger_1.logger.info(`Added AI provider: ${config.name} (${config.type})`);
    }
    async removeProvider(providerId) {
        this.providers.delete(providerId);
        logger_1.logger.info(`Removed AI provider: ${providerId}`);
    }
    async getProvider(providerId) {
        return this.providers.get(providerId);
    }
    async listProviders() {
        return Array.from(this.providers.values());
    }
    async setTaskConfig(taskType, config) {
        this.taskConfigs.set(taskType, config);
        logger_1.logger.info(`Set task config for: ${taskType}`);
    }
    async getTaskConfig(taskType) {
        return this.taskConfigs.get(taskType);
    }
    async generateText(prompt, taskType, options) {
        try {
            // Get task configuration
            const taskConfig = taskType ? await this.getTaskConfig(taskType) : undefined;
            // Determine which provider to use
            const providerId = options?.providerId || taskConfig?.providerId || 'default';
            const provider = await this.getProvider(providerId);
            if (!provider) {
                throw new Error(`Provider ${providerId} not found`);
            }
            // For now, return a mock response
            // In a real implementation, this would call the actual AI provider
            const response = {
                content: `Mock response for prompt: ${prompt.substring(0, 50)}...`,
                usage: {
                    promptTokens: prompt.length / 4,
                    completionTokens: 100,
                    totalTokens: (prompt.length / 4) + 100
                },
                model: options?.model || taskConfig?.model || provider.defaultModel,
                provider: provider.name
            };
            logger_1.logger.info(`Generated text using ${provider.name} (${response.model})`);
            return response;
        }
        catch (error) {
            logger_1.logger.error('Failed to generate text:', error);
            throw error;
        }
    }
    async testProvider(providerId) {
        try {
            const provider = await this.getProvider(providerId);
            if (!provider) {
                return false;
            }
            // Test with a simple prompt
            await this.generateText('Test prompt', undefined, { providerId });
            return true;
        }
        catch (error) {
            logger_1.logger.error(`Provider test failed for ${providerId}:`, error);
            return false;
        }
    }
}
exports.EnhancedAIProvider = EnhancedAIProvider;
// Export singleton instance
exports.enhancedAIProvider = new EnhancedAIProvider();
exports.default = exports.enhancedAIProvider;
//# sourceMappingURL=enhanced-ai-provider.js.map