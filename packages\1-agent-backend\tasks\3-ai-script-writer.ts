import { aiProvider } from '../core/ai-provider';
import { databaseHandler } from '../core/database-handler';
import { logger } from '../core/logger';
import { aiIdeaAnalyzer, IdeaAnalysis } from './2-ai-idea-analyzer';

export interface VideoScript {
  title: string;
  hook: string;
  introduction: string;
  mainContent: ScriptSection[];
  conclusion: string;
  callToAction: string;
  estimatedDuration: number;
  visualCues: VisualCue[];
  audioCues: AudioCue[];
}

export interface ScriptSection {
  timestamp: string;
  content: string;
  visualDescription: string;
  emphasis: 'normal' | 'strong' | 'dramatic';
}

export interface VisualCue {
  timestamp: string;
  type: 'text-overlay' | 'image' | 'transition' | 'effect';
  description: string;
  duration: number;
}

export interface AudioCue {
  timestamp: string;
  type: 'music' | 'sound-effect' | 'voice-emphasis';
  description: string;
  volume: number;
}

export class AIScriptWriter {
  async generateScript(projectId: string): Promise<VideoScript> {
    try {
      logger.info(`Generating script for project ${projectId}...`);

      const project = await databaseHandler.getVideoProject(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      // Get idea analysis first
      const analysis = await aiIdeaAnalyzer.getAnalysisForProject(projectId);
      if (!analysis) {
        throw new Error(`No analysis found for project ${projectId}`);
      }

      const scriptPrompt = `
        You are an expert script writer for short-form video content (TikTok, YouTube Shorts, Instagram Reels).
        
        Project Details:
        Title: ${project.title}
        Description: ${project.description}
        Target Audience: ${analysis.targetAudience}
        Estimated Duration: ${analysis.estimatedDuration} seconds
        Key Points: ${analysis.keyPoints.join(', ')}
        Suggested Hooks: ${analysis.hooks.join(', ')}
        
        Write a compelling script in JSON format:
        {
          "title": "Final video title",
          "hook": "Attention-grabbing opening (3-5 seconds)",
          "introduction": "Brief introduction (5-10 seconds)",
          "mainContent": [
            {
              "timestamp": "0:15",
              "content": "Main point content",
              "visualDescription": "What viewers should see",
              "emphasis": "normal/strong/dramatic"
            }
          ],
          "conclusion": "Wrap-up and summary",
          "callToAction": "Specific call to action",
          "estimatedDuration": ${analysis.estimatedDuration},
          "visualCues": [
            {
              "timestamp": "0:05",
              "type": "text-overlay",
              "description": "Key point text",
              "duration": 3
            }
          ],
          "audioCues": [
            {
              "timestamp": "0:00",
              "type": "music",
              "description": "Upbeat background music",
              "volume": 0.3
            }
          ]
        }
        
        Guidelines:
        - Keep it engaging and fast-paced
        - Use simple, clear language
        - Include specific visual and audio directions
        - Optimize for mobile viewing
        - Include retention hooks throughout
      `;

      const response = await aiProvider.generateText(scriptPrompt);
      
      let script: VideoScript;
      try {
        script = JSON.parse(response);
      } catch (parseError) {
        logger.error(projectId, `Failed to parse script: ${parseError}`);
        // Fallback to basic script
        script = this.generateBasicScript(project.title, project.description || '', analysis);
      }

      // Save script to project
      await databaseHandler.updateVideoProject(projectId, {
        description: `${project.description}\n\nScript generated: ${script.title}`,
      });

      logger.info(`Generated script for project ${projectId}: "${script.title}"`);
      return script;

    } catch (error) {
      logger.error(projectId, `Script generation failed: ${error}`);
      throw error;
    }
  }

  async processProjectsNeedingScripts(): Promise<void> {
    try {
      const projects = await databaseHandler.getVideoProjects('SCRIPT_CREATED' as any);
      
      for (const project of projects) {
        try {
          await this.generateScript(project.id);
          
          // Update status to indicate script is ready
          await databaseHandler.updateVideoProjectStatus(project.id, 'PENDING_VALIDATION' as any);
          
        } catch (error) {
          logger.error(project.id, `Failed to generate script: ${error}`);
          await databaseHandler.updateVideoProjectStatus(project.id, 'ERROR' as any, `Script generation failed: ${error}`);
        }
      }

    } catch (error) {
      logger.error('', `Failed to process projects needing scripts: ${error}`);
      throw error;
    }
  }

  private generateBasicScript(title: string, description: string, analysis: IdeaAnalysis): VideoScript {
    const duration = analysis.estimatedDuration;
    
    return {
      title: title,
      hook: analysis.hooks[0] || "Check this out!",
      introduction: `Today I'm going to show you ${title.toLowerCase()}`,
      mainContent: analysis.keyPoints.map((point, index) => ({
        timestamp: `0:${String(10 + index * 15).padStart(2, '0')}`,
        content: point,
        visualDescription: `Show relevant visuals for: ${point}`,
        emphasis: index === 0 ? 'strong' : 'normal'
      })),
      conclusion: "That's it! Hope this was helpful.",
      callToAction: analysis.callToAction,
      estimatedDuration: duration,
      visualCues: [
        {
          timestamp: "0:00",
          type: "text-overlay",
          description: title,
          duration: 3
        },
        {
          timestamp: `0:${String(duration - 5).padStart(2, '0')}`,
          type: "text-overlay", 
          description: "Like & Follow!",
          duration: 3
        }
      ],
      audioCues: [
        {
          timestamp: "0:00",
          type: "music",
          description: "Upbeat background music",
          volume: 0.3
        }
      ]
    };
  }

  async optimizeScript(script: VideoScript): Promise<VideoScript> {
    try {
      // Optimize for better engagement
      const optimizedScript = { ...script };
      
      // Ensure hook is under 5 seconds
      if (script.hook.length > 50) {
        optimizedScript.hook = script.hook.substring(0, 47) + "...";
      }
      
      // Add retention elements
      if (optimizedScript.mainContent.length > 2) {
        optimizedScript.mainContent[Math.floor(optimizedScript.mainContent.length / 2)].emphasis = 'dramatic';
      }
      
      return optimizedScript;
      
    } catch (error) {
      logger.error('', `Script optimization failed: ${error}`);
      return script;
    }
  }

  async validateScript(script: VideoScript): Promise<{ isValid: boolean; issues: string[] }> {
    const issues: string[] = [];
    
    if (!script.hook || script.hook.length < 10) {
      issues.push("Hook is too short or missing");
    }
    
    if (script.mainContent.length === 0) {
      issues.push("No main content sections");
    }
    
    if (script.estimatedDuration > 90) {
      issues.push("Script is too long for short-form content");
    }
    
    if (script.estimatedDuration < 15) {
      issues.push("Script is too short");
    }
    
    if (!script.callToAction) {
      issues.push("Missing call to action");
    }
    
    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

export const aiScriptWriter = new AIScriptWriter();
