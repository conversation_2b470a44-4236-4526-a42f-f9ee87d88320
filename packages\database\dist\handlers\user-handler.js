"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userHandler = exports.UserHandler = void 0;
const client_1 = require("../client");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
class UserHandler {
    async createUser(data) {
        const hashedPassword = await bcryptjs_1.default.hash(data.password, 12);
        return await client_1.prisma.user.create({
            data: {
                ...data,
                password: hashedPassword
            }
        });
    }
    async getUserById(id) {
        return await client_1.prisma.user.findUnique({
            where: { id }
        });
    }
    async getUserByEmail(email) {
        return await client_1.prisma.user.findUnique({
            where: { email }
        });
    }
    async updateUser(id, data) {
        return await client_1.prisma.user.update({
            where: { id },
            data
        });
    }
    async deleteUser(id) {
        return await client_1.prisma.user.delete({
            where: { id }
        });
    }
    async verifyPassword(plainPassword, hashedPassword) {
        return await bcryptjs_1.default.compare(plainPassword, hashedPassword);
    }
    async updatePassword(id, newPassword) {
        const hashedPassword = await bcryptjs_1.default.hash(newPassword, 12);
        return await client_1.prisma.user.update({
            where: { id },
            data: {
                password: hashedPassword
            }
        });
    }
    async getUsers(options) {
        return await client_1.prisma.user.findMany({
            skip: options?.skip,
            take: options?.take,
            orderBy: options?.orderBy || { createdAt: 'desc' }
        });
    }
    async getUserCount() {
        return await client_1.prisma.user.count();
    }
}
exports.UserHandler = UserHandler;
exports.userHandler = new UserHandler();
exports.default = exports.userHandler;
//# sourceMappingURL=user-handler.js.map