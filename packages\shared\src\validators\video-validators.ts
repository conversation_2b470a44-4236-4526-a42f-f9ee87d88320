import { z } from 'zod';

export const createVideoProjectSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  aiProviderId: z.string().uuid('Invalid AI provider ID').optional(),
  voiceId: z.string().uuid('Invalid voice ID').optional(),
  script: z.string().max(10000, 'Script too long').optional(),
});

export const updateVideoProjectSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long').optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  aiProviderId: z.string().uuid('Invalid AI provider ID').optional(),
  voiceId: z.string().uuid('Invalid voice ID').optional(),
  script: z.string().max(10000, 'Script too long').optional(),
  status: z.enum(['PENDING', 'PROCESSING', 'COMPLETED', 'ERROR']).optional(),
});

export const videoProjectQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  status: z.enum(['PENDING', 'PROCESSING', 'COMPLETED', 'ERROR']).optional(),
  search: z.string().max(100).optional(),
});

export type CreateVideoProjectInput = z.infer<typeof createVideoProjectSchema>;
export type UpdateVideoProjectInput = z.infer<typeof updateVideoProjectSchema>;
export type VideoProjectQueryInput = z.infer<typeof videoProjectQuerySchema>;
