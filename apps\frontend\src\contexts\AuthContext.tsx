'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Cookies from 'js-cookie';
import { api } from '@/lib/api';
import { User } from '@piknowkyo/shared/types';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name?: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const isAuthenticated = !!user;

  // Check if user is authenticated on mount
  useEffect(() => {
    const token = Cookies.get('auth_token');
    if (token) {
      fetchUser();
    } else {
      setIsLoading(false);
    }
  }, []);

  const fetchUser = async () => {
    try {
      const response = await api.get('/users/profile');
      setUser(response.data.data);
    } catch (error) {
      // Token is invalid, remove it
      Cookies.remove('auth_token');
      Cookies.remove('refresh_token');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      const response = await api.post('/auth/login', { email, password });
      const { user: userData, token, refreshToken } = response.data.data;

      // Store tokens
      Cookies.set('auth_token', token, { expires: 1 }); // 1 day
      Cookies.set('refresh_token', refreshToken, { expires: 7 }); // 7 days

      setUser(userData);
      router.push('/dashboard');
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Login failed');
    }
  };

  const register = async (email: string, password: string, name?: string) => {
    try {
      const response = await api.post('/auth/register', { email, password, name });
      const { user: userData, token, refreshToken } = response.data.data;

      // Store tokens
      Cookies.set('auth_token', token, { expires: 1 }); // 1 day
      Cookies.set('refresh_token', refreshToken, { expires: 7 }); // 7 days

      setUser(userData);
      // Don't redirect automatically - let the calling component handle it
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  };

  const logout = () => {
    // Remove tokens
    Cookies.remove('auth_token');
    Cookies.remove('refresh_token');

    setUser(null);
    router.push('/auth/login');
  };

  const refreshToken = async () => {
    try {
      const refreshTokenValue = Cookies.get('refresh_token');
      if (!refreshTokenValue) {
        throw new Error('No refresh token');
      }

      const response = await api.post('/auth/refresh', {
        refreshToken: refreshTokenValue,
      });

      const { token, refreshToken: newRefreshToken } = response.data.data;

      // Update tokens
      Cookies.set('auth_token', token, { expires: 1 }); // 1 day
      Cookies.set('refresh_token', newRefreshToken, { expires: 7 }); // 7 days
    } catch (error) {
      // Refresh failed, logout user
      logout();
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
