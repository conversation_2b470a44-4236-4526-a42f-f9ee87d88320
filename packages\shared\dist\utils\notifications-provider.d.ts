export interface NotificationConfig {
    email?: {
        enabled: boolean;
        smtp?: {
            host: string;
            port: number;
            secure: boolean;
            auth: {
                user: string;
                pass: string;
            };
        };
    };
    webhook?: {
        enabled: boolean;
        url: string;
        headers?: Record<string, string>;
    };
    slack?: {
        enabled: boolean;
        webhookUrl: string;
    };
    discord?: {
        enabled: boolean;
        webhookUrl: string;
    };
}
export interface Notification {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    data?: any;
    timestamp: Date;
    userId?: string;
}
export declare class NotificationsProvider {
    private config;
    constructor();
    private initializeProvider;
    sendNotification(notification: Omit<Notification, 'id' | 'timestamp'>): Promise<void>;
    private sendEmailNotification;
    private sendWebhookNotification;
    private sendSlackNotification;
    private sendDiscordNotification;
    sendProjectStatusUpdate(projectId: string, status: string, userId?: string): Promise<void>;
    sendErrorNotification(error: Error, context?: string, userId?: string): Promise<void>;
    sendSuccessNotification(title: string, message: string, userId?: string): Promise<void>;
    private generateId;
    updateConfig(newConfig: Partial<NotificationConfig>): void;
    getConfig(): NotificationConfig;
}
export declare const notificationsProvider: NotificationsProvider;
export default notificationsProvider;
//# sourceMappingURL=notifications-provider.d.ts.map