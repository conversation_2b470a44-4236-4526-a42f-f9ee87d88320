import { Router, Response } from 'express';
import { body, param, query } from 'express-validator';
import { validate } from '../middleware/validation';
import { asyncHandler } from '../utils/async-handler';
import { requireAdmin, AuthenticatedRequest } from '../middleware/auth';
import { VoiceService } from '../services/voice/voice.service';
import { TTSProviderType, Language } from '@piknowkyo/shared/types';
import { prisma } from '@piknowkyo/database/client';
import { createError } from '../middleware/error-handler';
import { ERROR_CODES } from '@piknowkyo/shared/constants';

const router = Router();
const voiceService = new VoiceService();

// Validation rules
const createProviderValidation = [
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Provider name must be between 1 and 100 characters'),
  body('type')
    .isIn(['AZURE', 'ELEVENLABS', 'GOOGLE', 'CUSTOM'])
    .withMessage('Invalid provider type'),
  body('baseUrl')
    .optional()
    .isURL()
    .withMessage('Base URL must be a valid URL'),
  body('apiKeys')
    .isArray({ min: 1 })
    .withMessage('At least one API key is required'),
  body('apiKeys.*')
    .isLength({ min: 10 })
    .withMessage('API keys must be at least 10 characters long'),
  body('config')
    .optional()
    .isObject()
    .withMessage('Config must be an object')
];

const previewValidation = [
  body('text')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('Text must be between 1 and 500 characters'),
  body('voiceId')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Voice ID is required'),
  body('provider')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Provider is required'),
  body('language')
    .isIn(['FRENCH', 'ENGLISH', 'SPANISH'])
    .withMessage('Invalid language'),
  body('settings')
    .optional()
    .isObject()
    .withMessage('Settings must be an object'),
  body('settings.speed')
    .optional()
    .isFloat({ min: 0.25, max: 4.0 })
    .withMessage('Speed must be between 0.25 and 4.0'),
  body('settings.pitch')
    .optional()
    .isFloat({ min: -20, max: 20 })
    .withMessage('Pitch must be between -20 and 20'),
  body('settings.volume')
    .optional()
    .isFloat({ min: -20, max: 20 })
    .withMessage('Volume must be between -20 and 20')
];

const synthesisValidation = [
  body('text')
    .trim()
    .isLength({ min: 1, max: 5000 })
    .withMessage('Text must be between 1 and 5000 characters'),
  body('voiceId')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Voice ID is required'),
  body('settings')
    .isObject()
    .withMessage('Settings are required'),
  body('settings.provider')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Provider is required'),
  body('settings.speed')
    .isFloat({ min: 0.25, max: 4.0 })
    .withMessage('Speed must be between 0.25 and 4.0'),
  body('outputFormat')
    .optional()
    .isIn(['mp3', 'wav', 'ogg'])
    .withMessage('Invalid output format')
];

// GET /api/voices/providers
router.get('/providers', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const providers = await voiceService.getProviders();

  res.json({
    success: true,
    data: providers,
    timestamp: new Date().toISOString()
  });
}));

// GET /api/voices/providers/:id
router.get('/providers/:id', validate([
  param('id').isUUID().withMessage('Invalid provider ID')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const provider = await voiceService.getProvider(req.params.id);

  if (!provider) {
    return res.status(404).json({
      success: false,
      error: 'NOT_FOUND',
      message: 'Provider not found',
      timestamp: new Date().toISOString()
    });
  }

  res.json({
    success: true,
    data: provider,
    timestamp: new Date().toISOString()
  });
}));

// POST /api/voices/providers
router.post('/providers', requireAdmin, validate(createProviderValidation), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { name, type, baseUrl, apiKeys, config } = req.body;

  const provider = await voiceService.upsertProvider({
    name,
    type: type as TTSProviderType,
    baseUrl,
    isCustom: type === 'CUSTOM',
    config,
    apiKeys
  });

  res.status(201).json({
    success: true,
    data: provider,
    message: 'TTS provider created successfully',
    timestamp: new Date().toISOString()
  });
}));

// POST /api/voices/providers/:id/fetch-voices
router.post('/providers/:id/fetch-voices', requireAdmin, validate([
  param('id').isUUID().withMessage('Invalid provider ID')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const voices = await voiceService.fetchVoices(req.params.id);

  res.json({
    success: true,
    data: voices,
    message: 'Voices fetched successfully',
    timestamp: new Date().toISOString()
  });
}));

// GET /api/voices
router.get('/', validate([
  query('language')
    .optional()
    .isIn(['FRENCH', 'ENGLISH', 'SPANISH'])
    .withMessage('Invalid language')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { language } = req.query;

  if (language) {
    const voices = await voiceService.getVoicesByLanguage(language as Language);
    
    res.json({
      success: true,
      data: voices,
      timestamp: new Date().toISOString()
    });
  } else {
    // Get all voices grouped by language
    const [frenchVoices, englishVoices, spanishVoices] = await Promise.all([
      voiceService.getVoicesByLanguage('FRENCH'),
      voiceService.getVoicesByLanguage('ENGLISH'),
      voiceService.getVoicesByLanguage('SPANISH')
    ]);

    res.json({
      success: true,
      data: {
        FRENCH: frenchVoices,
        ENGLISH: englishVoices,
        SPANISH: spanishVoices
      },
      timestamp: new Date().toISOString()
    });
  }
}));

// POST /api/voices/preview
router.post('/preview', validate(previewValidation), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { text, voiceId, provider, language, settings } = req.body;

  const preview = await voiceService.generatePreview({
    text,
    voiceId,
    provider,
    language: language as Language,
    settings
  });

  res.json({
    success: true,
    data: preview,
    message: 'Voice preview generated successfully',
    timestamp: new Date().toISOString()
  });
}));

// POST /api/voices/synthesize
router.post('/synthesize', validate(synthesisValidation), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const { text, voiceId, settings, outputFormat } = req.body;

  const result = await voiceService.synthesizeSpeech({
    text,
    voiceId,
    settings,
    outputFormat
  });

  res.json({
    success: true,
    data: result,
    message: 'Speech synthesized successfully',
    timestamp: new Date().toISOString()
  });
}));

// GET /api/voices/types
router.get('/types', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const types = [
    {
      value: 'AZURE',
      label: 'Azure Speech Services',
      description: 'Microsoft Azure Text-to-Speech with neural voices'
    },
    {
      value: 'ELEVENLABS',
      label: 'ElevenLabs',
      description: 'High-quality AI voice synthesis with voice cloning'
    },
    {
      value: 'GOOGLE',
      label: 'Google Text-to-Speech',
      description: 'Google Cloud Text-to-Speech with WaveNet voices'
    },
    {
      value: 'CUSTOM',
      label: 'Custom Provider',
      description: 'Custom TTS provider with compatible API'
    }
  ];

  res.json({
    success: true,
    data: types,
    timestamp: new Date().toISOString()
  });
}));

// GET /api/voices/languages
router.get('/languages', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const languages = [
    {
      value: 'FRENCH',
      label: 'Français',
      code: 'fr',
      flag: '🇫🇷'
    },
    {
      value: 'ENGLISH',
      label: 'English',
      code: 'en',
      flag: '🇺🇸'
    },
    {
      value: 'SPANISH',
      label: 'Español',
      code: 'es',
      flag: '🇪🇸'
    }
  ];

  res.json({
    success: true,
    data: languages,
    timestamp: new Date().toISOString()
  });
}));

// GET /api/voices/settings
router.get('/settings', asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;

  // Get user settings
  const userSettings = await prisma.userSettings.findUnique({
    where: { userId },
    include: {
      voiceSettings: {
        include: {
          ttsProvider: {
            select: {
              id: true,
              name: true,
              type: true
            }
          }
        }
      }
    }
  });

  const voiceSettings = userSettings?.voiceSettings || [];

  res.json({
    success: true,
    data: voiceSettings,
    timestamp: new Date().toISOString()
  });
}));

// POST /api/voices/settings
router.post('/settings', validate([
  body('language')
    .isIn(['FRENCH', 'ENGLISH', 'SPANISH'])
    .withMessage('Invalid language'),
  body('ttsProviderId')
    .isUUID()
    .withMessage('Invalid TTS provider ID'),
  body('voiceId')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Voice ID is required'),
  body('speed')
    .optional()
    .isFloat({ min: 0.25, max: 4.0 })
    .withMessage('Speed must be between 0.25 and 4.0'),
  body('pitch')
    .optional()
    .isFloat({ min: -20, max: 20 })
    .withMessage('Pitch must be between -20 and 20'),
  body('volume')
    .optional()
    .isFloat({ min: -20, max: 20 })
    .withMessage('Volume must be between -20 and 20')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { language, ttsProviderId, voiceId, speed = 1.0, pitch = 0.0, volume = 0.0 } = req.body;

  // Verify TTS provider exists
  const ttsProvider = await prisma.tTSProvider.findUnique({
    where: { id: ttsProviderId }
  });

  if (!ttsProvider) {
    throw createError('TTS provider not found', 404, ERROR_CODES.NOT_FOUND);
  }

  // Ensure user settings exist
  const userSettings = await prisma.userSettings.upsert({
    where: { userId },
    create: { userId },
    update: {}
  });

  // Create voice setting
  const voiceSetting = await prisma.userVoiceSettings.create({
    data: {
      language,
      speed,
      pitch,
      volume,
      userSettingsId: userSettings.id,
      ttsProviderId,
      voiceId
    },
    include: {
      ttsProvider: {
        select: {
          id: true,
          name: true,
          type: true
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    data: voiceSetting,
    message: 'Voice setting created successfully',
    timestamp: new Date().toISOString()
  });
}));

// PUT /api/voices/settings/:id
router.put('/settings/:id', validate([
  param('id').isUUID().withMessage('Invalid setting ID'),
  body('speed')
    .optional()
    .isFloat({ min: 0.25, max: 4.0 })
    .withMessage('Speed must be between 0.25 and 4.0'),
  body('pitch')
    .optional()
    .isFloat({ min: -20, max: 20 })
    .withMessage('Pitch must be between -20 and 20'),
  body('volume')
    .optional()
    .isFloat({ min: -20, max: 20 })
    .withMessage('Volume must be between -20 and 20'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  const { speed, pitch, volume, isActive } = req.body;

  // Verify the setting belongs to the user
  const existingSetting = await prisma.userVoiceSettings.findFirst({
    where: {
      id,
      userSettings: {
        userId
      }
    }
  });

  if (!existingSetting) {
    throw createError('Voice setting not found', 404, ERROR_CODES.NOT_FOUND);
  }

  // Update voice setting
  const voiceSetting = await prisma.userVoiceSettings.update({
    where: { id },
    data: {
      ...(speed !== undefined && { speed }),
      ...(pitch !== undefined && { pitch }),
      ...(volume !== undefined && { volume }),
      ...(isActive !== undefined && { isActive })
    },
    include: {
      ttsProvider: {
        select: {
          id: true,
          name: true,
          type: true
        }
      }
    }
  });

  res.json({
    success: true,
    data: voiceSetting,
    message: 'Voice setting updated successfully',
    timestamp: new Date().toISOString()
  });
}));

// DELETE /api/voices/settings/:id
router.delete('/settings/:id', validate([
  param('id').isUUID().withMessage('Invalid setting ID')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { id } = req.params;

  // Verify the setting belongs to the user
  const existingSetting = await prisma.userVoiceSettings.findFirst({
    where: {
      id,
      userSettings: {
        userId
      }
    }
  });

  if (!existingSetting) {
    throw createError('Voice setting not found', 404, ERROR_CODES.NOT_FOUND);
  }

  // Delete voice setting
  await prisma.userVoiceSettings.delete({
    where: { id }
  });

  res.json({
    success: true,
    message: 'Voice setting deleted successfully',
    timestamp: new Date().toISOString()
  });
}));

export default router;
