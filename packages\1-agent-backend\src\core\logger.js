"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel[LogLevel["DEBUG"] = 0] = "DEBUG";
    LogLevel[LogLevel["INFO"] = 1] = "INFO";
    LogLevel[LogLevel["WARN"] = 2] = "WARN";
    LogLevel[LogLevel["ERROR"] = 3] = "ERROR";
})(LogLevel || (LogLevel = {}));
var currentLogLevel = LogLevel[process.env.LOG_LEVEL || 'INFO'] || LogLevel.INFO;
var Logger = /** @class */ (function () {
    function Logger() {
    }
    Logger.prototype.formatMessage = function (level, message, args) {
        var timestamp = new Date().toISOString();
        return "[".concat(timestamp, "] [").concat(level, "] ").concat(message);
    };
    Logger.prototype.debug = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (currentLogLevel <= LogLevel.DEBUG) {
            console.debug.apply(console, __spreadArray([this.formatMessage('DEBUG', message, args)], args, false));
        }
    };
    Logger.prototype.info = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (currentLogLevel <= LogLevel.INFO) {
            console.info.apply(console, __spreadArray([this.formatMessage('INFO', message, args)], args, false));
        }
    };
    Logger.prototype.warn = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (currentLogLevel <= LogLevel.WARN) {
            console.warn.apply(console, __spreadArray([this.formatMessage('WARN', message, args)], args, false));
        }
    };
    Logger.prototype.error = function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (currentLogLevel <= LogLevel.ERROR) {
            console.error.apply(console, __spreadArray([this.formatMessage('ERROR', message, args)], args, false));
        }
    };
    return Logger;
}());
exports.logger = new Logger();
