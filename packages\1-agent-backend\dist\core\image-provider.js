"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.imageProvider = exports.ImageProvider = void 0;
const dotenv = __importStar(require("dotenv"));
const logger_1 = require("./logger");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
dotenv.config();
class ImageProvider {
    constructor() {
        this.stabilityApiKey = process.env.STABILITY_AI_API_KEY;
        this.midjourneyApiKey = process.env.MIDJOURNEY_API_KEY;
        this.outputDir = process.env.CONTENT_OUTPUT_DIR || './output';
        // Ensure output directory exists
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }
    async generateImage(prompt, options = {}) {
        try {
            // Try Stability AI first if available
            if (this.stabilityApiKey) {
                return await this.generateWithStabilityAI(prompt, options);
            }
            // Fall back to Midjourney if available
            if (this.midjourneyApiKey) {
                return await this.generateWithMidjourney(prompt, options);
            }
            // Fall back to mock image generation
            return await this.generateMockImage(prompt, options);
        }
        catch (error) {
            logger_1.logger.error('', `Image generation failed: ${error}`);
            return {
                success: false,
                error: `Image generation failed: ${error}`,
            };
        }
    }
    async generateWithStabilityAI(prompt, options) {
        try {
            logger_1.logger.info('Generating image with Stability AI...');
            const fileName = `image_${Date.now()}.png`;
            const filePath = path.join(this.outputDir, fileName);
            // TODO: Implement actual Stability AI API call
            // const response = await fetch('https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image', {
            //   method: 'POST',
            //   headers: {
            //     'Content-Type': 'application/json',
            //     'Accept': 'application/json',
            //     'Authorization': `Bearer ${this.stabilityApiKey}`,
            //   },
            //   body: JSON.stringify({
            //     text_prompts: [{ text: prompt }],
            //     cfg_scale: 7,
            //     height: options.height || 1024,
            //     width: options.width || 1024,
            //     steps: options.steps || 30,
            //     samples: 1,
            //   }),
            // });
            // Mock: Create a placeholder image file
            const mockImageContent = `Mock image generated for prompt: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`;
            fs.writeFileSync(filePath, mockImageContent);
            logger_1.logger.info(`Stability AI image generated: ${filePath}`);
            return {
                success: true,
                imageFilePath: filePath,
                width: options.width || 1024,
                height: options.height || 1024,
            };
        }
        catch (error) {
            logger_1.logger.error('', `Stability AI image generation failed: ${error}`);
            throw error;
        }
    }
    async generateWithMidjourney(prompt, options) {
        try {
            logger_1.logger.info('Generating image with Midjourney...');
            const fileName = `image_${Date.now()}.png`;
            const filePath = path.join(this.outputDir, fileName);
            // TODO: Implement actual Midjourney API call
            // Note: Midjourney doesn't have an official API, so this would use a third-party service
            // Mock: Create a placeholder image file
            const mockImageContent = `Mock Midjourney image for prompt: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"`;
            fs.writeFileSync(filePath, mockImageContent);
            logger_1.logger.info(`Midjourney image generated: ${filePath}`);
            return {
                success: true,
                imageFilePath: filePath,
                width: options.width || 1024,
                height: options.height || 1024,
            };
        }
        catch (error) {
            logger_1.logger.error('', `Midjourney image generation failed: ${error}`);
            throw error;
        }
    }
    async generateMockImage(prompt, options) {
        try {
            logger_1.logger.info('Generating mock image...');
            const fileName = `image_${Date.now()}.png`;
            const filePath = path.join(this.outputDir, fileName);
            // Mock: Create a placeholder image file
            const mockImageContent = `Mock image generated locally for prompt: "${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}"
Width: ${options.width || 1024}
Height: ${options.height || 1024}
Style: ${options.style || 'default'}
Quality: ${options.quality || 'medium'}`;
            fs.writeFileSync(filePath, mockImageContent);
            logger_1.logger.info(`Mock image generated: ${filePath}`);
            return {
                success: true,
                imageFilePath: filePath,
                width: options.width || 1024,
                height: options.height || 1024,
            };
        }
        catch (error) {
            logger_1.logger.error('', `Mock image generation failed: ${error}`);
            throw error;
        }
    }
    async generateThumbnail(prompt) {
        return await this.generateImage(prompt, {
            width: 1280,
            height: 720,
            quality: 'high',
            style: 'thumbnail',
        });
    }
    async generateBackgroundImage(prompt) {
        return await this.generateImage(prompt, {
            width: 1920,
            height: 1080,
            quality: 'high',
            style: 'background',
        });
    }
    async generateSquareImage(prompt) {
        return await this.generateImage(prompt, {
            width: 1024,
            height: 1024,
            quality: 'high',
            style: 'square',
        });
    }
    async testImageGeneration() {
        const testPrompt = "A beautiful landscape with mountains and a lake, digital art style, high quality";
        return await this.generateImage(testPrompt, {
            width: 512,
            height: 512,
            quality: 'medium',
        });
    }
    getOutputDirectory() {
        return this.outputDir;
    }
    cleanupOldFiles(maxAgeHours = 24) {
        try {
            const files = fs.readdirSync(this.outputDir);
            const now = Date.now();
            const maxAge = maxAgeHours * 60 * 60 * 1000;
            files.forEach(file => {
                if (file.startsWith('image_') && (file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg'))) {
                    const filePath = path.join(this.outputDir, file);
                    const stats = fs.statSync(filePath);
                    if (now - stats.mtime.getTime() > maxAge) {
                        fs.unlinkSync(filePath);
                        logger_1.logger.info(`Cleaned up old image file: ${file}`);
                    }
                }
            });
        }
        catch (error) {
            logger_1.logger.error('', `Failed to cleanup old image files: ${error}`);
        }
    }
    getSupportedFormats() {
        return ['png', 'jpg', 'jpeg', 'webp'];
    }
    getAvailableStyles() {
        return [
            'realistic',
            'digital-art',
            'anime',
            'cartoon',
            'oil-painting',
            'watercolor',
            'sketch',
            'photographic',
            'cinematic',
            'abstract',
        ];
    }
}
exports.ImageProvider = ImageProvider;
exports.imageProvider = new ImageProvider();
