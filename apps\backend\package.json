{"name": "@piknowkyo/backend", "version": "1.0.0", "description": "Backend API for Piknowkyo Generator", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate --schema=../../database/schema.prisma", "db:migrate": "prisma migrate dev --schema=../../database/schema.prisma", "db:deploy": "prisma migrate deploy --schema=../../database/schema.prisma", "db:reset": "prisma migrate reset --schema=../../database/schema.prisma --force", "db:seed": "tsx src/scripts/seed.ts", "db:studio": "prisma studio --schema=../../database/schema.prisma"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@piknowkyo/database": "file:../../packages/database", "@piknowkyo/shared": "file:../../packages/shared", "@prisma/client": "^5.0.0", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^16.3.0", "express": "^4.18.0", "express-rate-limit": "^6.8.0", "express-slow-down": "^2.1.0", "express-validator": "^7.0.0", "ffmpeg-static": "^5.1.0", "fluent-ffmpeg": "^2.1.2", "helmet": "^7.0.0", "ioredis": "^5.3.0", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "sharp": "^0.32.0", "winston": "^3.10.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/crypto-js": "^4.1.1", "@types/express": "^4.17.17", "@types/fluent-ffmpeg": "^2.1.21", "@types/jest": "^29.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^20.0.0", "@types/node-cron": "^3.0.8", "@types/supertest": "^2.0.12", "eslint": "^8.0.0", "jest": "^29.0.0", "prisma": "^5.0.0", "supertest": "^6.3.0", "tsx": "^3.12.0", "typescript": "^5.0.0"}}