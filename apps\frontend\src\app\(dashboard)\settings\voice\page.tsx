'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface VoiceProvider {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
  voices: Voice[];
}

interface Voice {
  id: string;
  name: string;
  language: string;
  gender: string;
  isActive: boolean;
}

interface VoiceSettings {
  id?: string;
  language: string;
  voiceId: string;
  speed: number;
  pitch: number;
  volume: number;
  isActive: boolean;
}

export default function VoiceSettingsPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [providers, setProviders] = useState<VoiceProvider[]>([]);
  const [settings, setSettings] = useState<VoiceSettings[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newSetting, setNewSetting] = useState<VoiceSettings>({
    language: 'ENGLISH',
    voiceId: '',
    speed: 1.0,
    pitch: 1.0,
    volume: 1.0,
    isActive: true
  });

  const languages = [
    { value: 'ENGLISH', label: 'English' },
    { value: 'FRENCH', label: 'French' },
    { value: 'SPANISH', label: 'Spanish' }
  ];

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      const [providersRes, settingsRes] = await Promise.all([
        fetch('/api/voices/providers'),
        fetch('/api/voices/settings')
      ]);

      if (providersRes.ok) {
        const providersData = await providersRes.json();
        setProviders(providersData.data || []);
      }

      if (settingsRes.ok) {
        const settingsData = await settingsRes.json();
        setSettings(settingsData.data || []);
      }
    } catch (error) {
      console.error('Error loading voice data:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleAddSetting = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/voices/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSetting),
      });

      if (response.ok) {
        await loadData();
        setShowAddForm(false);
        setNewSetting({
          language: 'ENGLISH',
          voiceId: '',
          speed: 1.0,
          pitch: 1.0,
          volume: 1.0,
          isActive: true
        });
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to add voice setting'}`);
      }
    } catch (error) {
      console.error('Error adding voice setting:', error);
      alert('Failed to add voice setting. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleToggleSetting = async (settingId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/voices/settings/${settingId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !isActive }),
      });

      if (response.ok) {
        await loadData();
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to update voice setting'}`);
      }
    } catch (error) {
      console.error('Error updating voice setting:', error);
      alert('Failed to update voice setting. Please try again.');
    }
  };

  const getAvailableVoices = () => {
    return providers
      .filter(p => p.isActive)
      .flatMap(p => p.voices.filter(v => v.isActive && v.language === newSetting.language));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-wide py-8">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Voice Settings</h1>
            <p className="mt-2 text-gray-600">
              Configure text-to-speech settings for different languages
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary"
          >
            Add Voice Setting
          </button>
        </div>

        {isLoadingData ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div className="space-y-6">
            {/* Voice Providers */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Voice Providers</h2>
              </div>
              <div className="card-body">
                {providers.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">No voice providers configured</p>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {providers.map(provider => (
                      <div key={provider.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-gray-900">{provider.name}</h3>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            provider.isActive 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {provider.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500">{provider.type}</p>
                        <p className="text-xs text-gray-400 mt-1">
                          {provider.voices.length} voices available
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Voice Settings */}
            <div className="card">
              <div className="card-header">
                <h2 className="text-lg font-medium text-gray-900">Language Voice Settings</h2>
              </div>
              <div className="card-body">
                {settings.length === 0 ? (
                  <div className="text-center py-8">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a2 2 0 012-2h6a2 2 0 012 2v6a3 3 0 01-3 3z" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No voice settings configured</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Add voice settings for different languages to get started.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {settings.map(setting => (
                      <div key={setting.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium text-gray-900">
                              {languages.find(l => l.value === setting.language)?.label || setting.language}
                            </h3>
                            <p className="text-sm text-gray-500">Voice ID: {setting.voiceId}</p>
                            <div className="flex gap-4 mt-2 text-xs text-gray-400">
                              <span>Speed: {setting.speed}x</span>
                              <span>Pitch: {setting.pitch}x</span>
                              <span>Volume: {setting.volume}x</span>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              setting.isActive 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {setting.isActive ? 'Active' : 'Inactive'}
                            </span>
                            <button
                              onClick={() => handleToggleSetting(setting.id!, setting.isActive)}
                              className={`btn-sm ${
                                setting.isActive ? 'btn-outline' : 'btn-primary'
                              }`}
                            >
                              {setting.isActive ? 'Disable' : 'Enable'}
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
              <form onSubmit={handleAddSetting}>
                <div className="p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Add Voice Setting</h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Language
                      </label>
                      <select
                        className="input-field"
                        value={newSetting.language}
                        onChange={(e) => setNewSetting(prev => ({ ...prev, language: e.target.value, voiceId: '' }))}
                      >
                        {languages.map(lang => (
                          <option key={lang.value} value={lang.value}>
                            {lang.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Voice
                      </label>
                      <select
                        className="input-field"
                        value={newSetting.voiceId}
                        onChange={(e) => setNewSetting(prev => ({ ...prev, voiceId: e.target.value }))}
                        required
                      >
                        <option value="">Select a voice</option>
                        {getAvailableVoices().map(voice => (
                          <option key={voice.id} value={voice.id}>
                            {voice.name} ({voice.gender})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Speed
                        </label>
                        <input
                          type="number"
                          min="0.5"
                          max="2.0"
                          step="0.1"
                          className="input-field"
                          value={newSetting.speed}
                          onChange={(e) => setNewSetting(prev => ({ ...prev, speed: parseFloat(e.target.value) }))}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Pitch
                        </label>
                        <input
                          type="number"
                          min="0.5"
                          max="2.0"
                          step="0.1"
                          className="input-field"
                          value={newSetting.pitch}
                          onChange={(e) => setNewSetting(prev => ({ ...prev, pitch: parseFloat(e.target.value) }))}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Volume
                        </label>
                        <input
                          type="number"
                          min="0.1"
                          max="2.0"
                          step="0.1"
                          className="input-field"
                          value={newSetting.volume}
                          onChange={(e) => setNewSetting(prev => ({ ...prev, volume: parseFloat(e.target.value) }))}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="px-6 py-4 bg-gray-50 flex gap-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="btn-outline flex-1"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary flex-1"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <LoadingSpinner size="sm" />
                        Adding...
                      </>
                    ) : (
                      'Add Setting'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
