'use client';

import { useState } from 'react';
import { useAuth } from '@/hooks/useAuth';

export default function TestAuthPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [result, setResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  
  const { register: registerUser, user, isAuthenticated } = useAuth();

  const handleAuthTest = async () => {
    if (!email || !password || !name) {
      setResult('❌ Please fill all fields');
      return;
    }

    setIsLoading(true);
    setResult('Testing useAuth register...');
    
    try {
      await registerUser(email, password, name);
      setResult(`✅ useAuth Success! User registered and logged in.`);
    } catch (error: any) {
      setResult(`❌ useAuth Error: ${error.message}`);
      console.error('Full error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <h2 className="text-2xl font-bold text-center mb-6">Test useAuth Hook</h2>
          
          <div className="mb-4 p-3 bg-gray-100 rounded">
            <p><strong>Current User:</strong> {user ? user.email : 'Not logged in'}</p>
            <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          </div>
          
          <div className="space-y-4">
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded"
              />
              <input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded"
              />
              <input
                type="password"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded"
              />
              <button
                onClick={handleAuthTest}
                disabled={isLoading}
                className="w-full bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700 disabled:opacity-50"
              >
                {isLoading ? 'Testing...' : 'Test useAuth Register'}
              </button>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-2">Result:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-96 whitespace-pre-wrap">
                {result || 'No test run yet'}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
