import { Router, Response } from 'express';
import { body, param, query } from 'express-validator';
import { prisma } from '@piknowkyo/database/client';
import { validate } from '../middleware/validation';
import { createError } from '../middleware/error-handler';
import { asyncHandler } from '../utils/async-handler';
import { AuthenticatedRequest } from '../middleware/auth';
import { ERROR_CODES } from '@piknowkyo/shared/constants';
import { VideoProjectStatus } from '@piknowkyo/shared/types';
import { Language } from '@piknowkyo/shared/types';

const router = Router();

// Validation rules
const createVideoValidation = [
  body('title')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('primaryLanguage')
    .isIn(['FRENCH', 'ENGLISH', 'SPANISH'])
    .withMessage('Invalid primary language'),
  body('targetLanguages')
    .isArray({ min: 1 })
    .withMessage('At least one target language is required'),
  body('targetLanguages.*')
    .isIn(['FRENCH', 'ENGLISH', 'SPANISH'])
    .withMessage('Invalid target language'),
  body('content')
    .trim()
    .isLength({ min: 10, max: 10000 })
    .withMessage('Content must be between 10 and 10000 characters'),
  body('settings')
    .optional()
    .isObject()
    .withMessage('Settings must be an object')
];

const updateVideoValidation = [
  param('id')
    .isUUID()
    .withMessage('Invalid video ID'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Title must be between 1 and 200 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('Description must be less than 1000 characters'),
  body('content')
    .optional()
    .trim()
    .isLength({ min: 10, max: 10000 })
    .withMessage('Content must be between 10 and 10000 characters'),
  body('settings')
    .optional()
    .isObject()
    .withMessage('Settings must be an object')
];

// GET /api/videos
router.get('/', validate([
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('status')
    .optional()
    .isIn(['DRAFT', 'PROCESSING', 'COMPLETED', 'FAILED'])
    .withMessage('Invalid status'),
  query('language')
    .optional()
    .isIn(['FRENCH', 'ENGLISH', 'SPANISH'])
    .withMessage('Invalid language')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { page = 1, limit = 20, status, language } = req.query;

  const where: any = { userId };
  
  if (status) {
    where.status = status;
  }
  
  if (language) {
    where.primaryLanguage = language;
  }

  const [videos, total] = await Promise.all([
    prisma.videoProject.findMany({
      where,
      include: {
        multilingualVideos: {
          select: {
            id: true,
            language: true,
            status: true,
            audioUrl: true,
            videoUrl: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip: (Number(page) - 1) * Number(limit),
      take: Number(limit)
    }),
    prisma.videoProject.count({ where })
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  res.json({
    success: true,
    data: videos,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    },
    timestamp: new Date().toISOString()
  });
}));

// GET /api/videos/:id
router.get('/:id', validate([
  param('id').isUUID().withMessage('Invalid video ID')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const videoId = req.params.id;

  const video = await prisma.videoProject.findFirst({
    where: {
      id: videoId,
      userId
    },
    include: {
      multilingualVideos: true
    }
  });

  if (!video) {
    throw createError('Video not found', 404, ERROR_CODES.NOT_FOUND);
  }

  res.json({
    success: true,
    data: video,
    timestamp: new Date().toISOString()
  });
}));

// POST /api/videos
router.post('/', validate(createVideoValidation), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const { title, description, primaryLanguage, targetLanguages, content, settings } = req.body;

  // Create video project
  const video = await prisma.videoProject.create({
    data: {
      userId,
      title,
      description,
      primaryLanguage: primaryLanguage as Language,
      // TODO: Add targetLanguages field to schema if needed
      // content and settings stored in metadata for now
      metadata: JSON.stringify({ content, settings, targetLanguages }),
      status: 'IDEA_GENERATED'
    },
    include: {
      multilingualVideos: true
    }
  });

  res.status(201).json({
    success: true,
    data: video,
    message: 'Video project created successfully',
    timestamp: new Date().toISOString()
  });
}));

// PUT /api/videos/:id
router.put('/:id', validate(updateVideoValidation), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const videoId = req.params.id;
  const { title, description, content, settings } = req.body;

  // Check if video exists and belongs to user
  const existingVideo = await prisma.videoProject.findFirst({
    where: {
      id: videoId,
      userId
    }
  });

  if (!existingVideo) {
    throw createError('Video not found', 404, ERROR_CODES.NOT_FOUND);
  }

  // Update video
  const video = await prisma.videoProject.update({
    where: { id: videoId },
    data: {
      ...(title && { title }),
      ...(description !== undefined && { description }),
      ...(content && { content }),
      ...(settings && { settings }),
      updatedAt: new Date()
    },
    include: {
      multilingualVideos: true
    }
  });

  res.json({
    success: true,
    data: video,
    message: 'Video project updated successfully',
    timestamp: new Date().toISOString()
  });
}));

// DELETE /api/videos/:id
router.delete('/:id', validate([
  param('id').isUUID().withMessage('Invalid video ID')
]), asyncHandler(async (req: AuthenticatedRequest, res) => {
  const userId = req.user!.id;
  const videoId = req.params.id;

  // Check if video exists and belongs to user
  const existingVideo = await prisma.videoProject.findFirst({
    where: {
      id: videoId,
      userId
    }
  });

  if (!existingVideo) {
    throw createError('Video not found', 404, ERROR_CODES.NOT_FOUND);
  }

  // Delete video and related data
  await prisma.videoProject.delete({
    where: { id: videoId }
  });

  res.json({
    success: true,
    message: 'Video project deleted successfully',
    timestamp: new Date().toISOString()
  });
}));

export default router;
