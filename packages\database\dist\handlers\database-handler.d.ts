import { VideoProject, User, AIProvider, Voice } from '../generated';
export declare class DatabaseHandler {
    getUser(id: string): Promise<User | null>;
    getUserByEmail(email: string): Promise<User | null>;
    createUser(data: {
        email: string;
        password: string;
        name?: string;
    }): Promise<User>;
    getVideoProject(id: string): Promise<VideoProject | null>;
    getVideoProjects(status?: string): Promise<VideoProject[]>;
    createVideoProject(data: {
        title: string;
        description?: string;
        userId: string;
        primaryLanguage?: string;
        status?: string;
        metadata?: string;
    }): Promise<VideoProject>;
    updateVideoProject(id: string, data: Partial<VideoProject>): Promise<VideoProject>;
    updateVideoProjectStatus(id: string, status: string, metadata?: string): Promise<VideoProject>;
    getAIProvider(id: string): Promise<AIProvider | null>;
    getAIProviders(): Promise<AIProvider[]>;
    getVoice(id: string): Promise<Voice | null>;
    getVoices(): Promise<Voice[]>;
    create(table: string, data: any): Promise<any>;
    findMany(table: string, options?: any): Promise<any[]>;
    findUnique(table: string, where: any): Promise<any>;
    update(table: string, where: any, data: any): Promise<any>;
    delete(table: string, where: any): Promise<any>;
    healthCheck(): Promise<boolean>;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
}
export declare const databaseHandler: DatabaseHandler;
export default databaseHandler;
//# sourceMappingURL=database-handler.d.ts.map