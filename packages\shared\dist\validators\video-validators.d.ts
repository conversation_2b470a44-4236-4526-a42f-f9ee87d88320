import { z } from 'zod';
export declare const createVideoProjectSchema: z.ZodObject<{
    title: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    aiProviderId: z.ZodOptional<z.ZodString>;
    voiceId: z.<PERSON>ptional<z.ZodString>;
    script: z.<PERSON>od<PERSON>ptional<z.ZodString>;
}, "strip", z.Zod<PERSON>ype<PERSON>ny, {
    title: string;
    description?: string | undefined;
    aiProviderId?: string | undefined;
    voiceId?: string | undefined;
    script?: string | undefined;
}, {
    title: string;
    description?: string | undefined;
    aiProviderId?: string | undefined;
    voiceId?: string | undefined;
    script?: string | undefined;
}>;
export declare const updateVideoProjectSchema: z.ZodObject<{
    title: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodString>;
    aiProviderId: z.ZodOptional<z.ZodString>;
    voiceId: z.ZodOptional<z.ZodString>;
    script: z.<PERSON>od<PERSON>ptional<z.ZodString>;
    status: z.ZodOptional<z.ZodEnum<["PENDING", "PROCESSING", "COMPLETED", "ERROR"]>>;
}, "strip", z.ZodTypeAny, {
    title?: string | undefined;
    status?: "ERROR" | "PENDING" | "PROCESSING" | "COMPLETED" | undefined;
    description?: string | undefined;
    aiProviderId?: string | undefined;
    voiceId?: string | undefined;
    script?: string | undefined;
}, {
    title?: string | undefined;
    status?: "ERROR" | "PENDING" | "PROCESSING" | "COMPLETED" | undefined;
    description?: string | undefined;
    aiProviderId?: string | undefined;
    voiceId?: string | undefined;
    script?: string | undefined;
}>;
export declare const videoProjectQuerySchema: z.ZodObject<{
    page: z.ZodOptional<z.ZodEffects<z.ZodString, number, string>>;
    limit: z.ZodOptional<z.ZodEffects<z.ZodString, number, string>>;
    status: z.ZodOptional<z.ZodEnum<["PENDING", "PROCESSING", "COMPLETED", "ERROR"]>>;
    search: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    status?: "ERROR" | "PENDING" | "PROCESSING" | "COMPLETED" | undefined;
    page?: number | undefined;
    limit?: number | undefined;
    search?: string | undefined;
}, {
    status?: "ERROR" | "PENDING" | "PROCESSING" | "COMPLETED" | undefined;
    page?: string | undefined;
    limit?: string | undefined;
    search?: string | undefined;
}>;
export type CreateVideoProjectInput = z.infer<typeof createVideoProjectSchema>;
export type UpdateVideoProjectInput = z.infer<typeof updateVideoProjectSchema>;
export type VideoProjectQueryInput = z.infer<typeof videoProjectQuerySchema>;
//# sourceMappingURL=video-validators.d.ts.map