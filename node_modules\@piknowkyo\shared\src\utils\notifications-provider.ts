import { logger } from './logger';

export interface NotificationConfig {
  email?: {
    enabled: boolean;
    smtp?: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
  };
  webhook?: {
    enabled: boolean;
    url: string;
    headers?: Record<string, string>;
  };
  slack?: {
    enabled: boolean;
    webhookUrl: string;
  };
  discord?: {
    enabled: boolean;
    webhookUrl: string;
  };
}

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  data?: any;
  timestamp: Date;
  userId?: string;
}

export class NotificationsProvider {
  private config: NotificationConfig = {
    email: { enabled: false },
    webhook: { enabled: false, url: '' },
    slack: { enabled: false, webhookUrl: '' },
    discord: { enabled: false, webhookUrl: '' }
  };

  constructor() {
    this.initializeProvider();
  }

  private initializeProvider() {
    logger.info('Initializing Notifications Provider...');
    
    // Load configuration from environment variables
    this.config = {
      email: {
        enabled: process.env.EMAIL_NOTIFICATIONS_ENABLED === 'true',
        smtp: process.env.SMTP_HOST ? {
          host: process.env.SMTP_HOST,
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER || '',
            pass: process.env.SMTP_PASS || ''
          }
        } : undefined
      },
      webhook: {
        enabled: process.env.WEBHOOK_NOTIFICATIONS_ENABLED === 'true',
        url: process.env.WEBHOOK_URL || ''
      },
      slack: {
        enabled: process.env.SLACK_NOTIFICATIONS_ENABLED === 'true',
        webhookUrl: process.env.SLACK_WEBHOOK_URL || ''
      },
      discord: {
        enabled: process.env.DISCORD_NOTIFICATIONS_ENABLED === 'true',
        webhookUrl: process.env.DISCORD_WEBHOOK_URL || ''
      }
    };
  }

  async sendNotification(notification: Omit<Notification, 'id' | 'timestamp'>): Promise<void> {
    const fullNotification: Notification = {
      ...notification,
      id: this.generateId(),
      timestamp: new Date()
    };

    logger.info(`Sending notification: ${fullNotification.title}`);

    try {
      // Send to all enabled channels
      const promises: Promise<void>[] = [];

      if (this.config.email?.enabled) {
        promises.push(this.sendEmailNotification(fullNotification));
      }

      if (this.config.webhook?.enabled) {
        promises.push(this.sendWebhookNotification(fullNotification));
      }

      if (this.config.slack?.enabled) {
        promises.push(this.sendSlackNotification(fullNotification));
      }

      if (this.config.discord?.enabled) {
        promises.push(this.sendDiscordNotification(fullNotification));
      }

      await Promise.allSettled(promises);
    } catch (error) {
      logger.error('Failed to send notification:', error);
    }
  }

  private async sendEmailNotification(notification: Notification): Promise<void> {
    // Mock email sending - implement with nodemailer or similar
    logger.info(`Email notification sent: ${notification.title}`);
  }

  private async sendWebhookNotification(notification: Notification): Promise<void> {
    // Mock webhook sending - implement with axios or fetch
    logger.info(`Webhook notification sent: ${notification.title}`);
  }

  private async sendSlackNotification(notification: Notification): Promise<void> {
    // Mock Slack notification - implement with Slack webhook
    logger.info(`Slack notification sent: ${notification.title}`);
  }

  private async sendDiscordNotification(notification: Notification): Promise<void> {
    // Mock Discord notification - implement with Discord webhook
    logger.info(`Discord notification sent: ${notification.title}`);
  }

  async sendProjectStatusUpdate(
    projectId: string,
    status: string,
    userId?: string
  ): Promise<void> {
    await this.sendNotification({
      type: 'info',
      title: 'Project Status Update',
      message: `Project ${projectId} status changed to ${status}`,
      data: { projectId, status },
      userId
    });
  }

  async sendErrorNotification(
    error: Error,
    context?: string,
    userId?: string
  ): Promise<void> {
    await this.sendNotification({
      type: 'error',
      title: 'Error Occurred',
      message: `${context ? context + ': ' : ''}${error.message}`,
      data: { error: error.stack, context },
      userId
    });
  }

  async sendSuccessNotification(
    title: string,
    message: string,
    userId?: string
  ): Promise<void> {
    await this.sendNotification({
      type: 'success',
      title,
      message,
      userId
    });
  }

  private generateId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  updateConfig(newConfig: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Notifications configuration updated');
  }

  getConfig(): NotificationConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const notificationsProvider = new NotificationsProvider();
export default notificationsProvider;
