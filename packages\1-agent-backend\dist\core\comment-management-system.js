"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.commentManagementSystem = exports.CommentManagementSystem = void 0;
const database_handler_1 = require("./database-handler");
const enhanced_ai_provider_1 = require("./enhanced-ai-provider");
const logger_1 = require("./logger");
const notifications_provider_1 = require("./notifications-provider");
class CommentManagementSystem {
    async analyzeComment(comment) {
        try {
            const analysisPrompt = `
        Analyze this comment for sentiment, intent, and response requirements:
        
        Comment: "${comment.content}"
        Platform: ${comment.platform}
        Author: ${comment.authorName}
        
        Provide analysis in JSON format:
        {
          "sentiment": "positive|negative|neutral",
          "topics": ["topic1", "topic2"],
          "intent": "question|compliment|complaint|suggestion|spam",
          "language": "language_code",
          "toxicity": 0.0,
          "requiresResponse": true,
          "urgency": "low|medium|high",
          "suggestedResponseTone": "professional|friendly|humorous|educational"
        }
        
        Consider:
        - Is this a genuine comment or spam?
        - Does it require a response?
        - What tone would be most appropriate?
        - How urgent is a response?
      `;
            const response = await enhanced_ai_provider_1.enhancedAIProvider.generateText(analysisPrompt, 'comment-analysis');
            try {
                const analysis = JSON.parse(response.content);
                return analysis;
            }
            catch {
                return this.getDefaultAnalysis(comment);
            }
        }
        catch (error) {
            logger_1.logger.error('', `Comment analysis failed: ${error}`);
            return this.getDefaultAnalysis(comment);
        }
    }
    async generateResponse(comment, analysis, customTone) {
        try {
            const tone = customTone || analysis.suggestedResponseTone;
            const responsePrompt = `
        Generate a ${tone} response to this comment:
        
        Original Comment: "${comment.content}"
        Platform: ${comment.platform}
        Sentiment: ${analysis.sentiment}
        Intent: ${analysis.intent}
        Topics: ${analysis.topics.join(', ')}
        
        Guidelines:
        - Keep it concise and appropriate for ${comment.platform}
        - Match the ${tone} tone
        - Be helpful and engaging
        - Include relevant information if it's a question
        - Thank them if it's a compliment
        - Address concerns professionally if it's a complaint
        - Use appropriate language for ${analysis.language}
        
        Response should be 1-3 sentences maximum.
      `;
            const response = await enhanced_ai_provider_1.enhancedAIProvider.generateText(responsePrompt, 'comment-response');
            const commentResponse = {
                id: `resp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                commentId: comment.id,
                content: response.content.trim(),
                tone: tone,
                language: analysis.language,
                status: 'draft',
                aiGenerated: true,
                generatedAt: new Date(),
                metadata: {
                    aiProvider: response.provider,
                    model: response.model,
                    confidence: 0.85, // Default confidence
                    keywords: analysis.topics
                }
            };
            // Save to database
            await database_handler_1.databaseHandler.createAIResponseDraft(comment.id, commentResponse.content);
            logger_1.logger.info(`Generated response for comment ${comment.id}`);
            return commentResponse;
        }
        catch (error) {
            logger_1.logger.error('', `Response generation failed: ${error}`);
            throw error;
        }
    }
    async processNewComments(videoProjectId) {
        try {
            // Get unprocessed comments
            const comments = await this.getUnprocessedComments(videoProjectId);
            for (const comment of comments) {
                try {
                    // Analyze comment
                    const analysis = await this.analyzeComment(comment);
                    // Skip spam comments
                    if (analysis.intent === 'spam' || analysis.toxicity > 0.7) {
                        await this.markCommentAsSpam(comment.id);
                        continue;
                    }
                    // Generate response if needed
                    if (analysis.requiresResponse) {
                        const response = await this.generateResponse(comment, analysis);
                        // Notify moderators for high-urgency comments
                        if (analysis.urgency === 'high') {
                            await notifications_provider_1.notificationsProvider.sendSlackNotification({
                                title: 'High Priority Comment',
                                message: `Comment requires urgent attention: "${comment.content}"`,
                                priority: 'high',
                                url: `https://dashboard.piknowkyo.com/comments/${comment.id}`
                            });
                        }
                    }
                    // Mark as processed
                    await this.markCommentAsProcessed(comment.id);
                }
                catch (error) {
                    logger_1.logger.error('', `Failed to process comment ${comment.id}: ${error}`);
                }
            }
        }
        catch (error) {
            logger_1.logger.error('', `Comment processing failed: ${error}`);
            throw error;
        }
    }
    async approveResponse(responseId, approvedBy) {
        try {
            // Update response status using Prisma directly
            await database_handler_1.databaseHandler.prisma.aIResponseDraft.update({
                where: { id: responseId },
                data: {
                    status: 'APPROVED'
                }
            });
            logger_1.logger.info(`Response ${responseId} approved by ${approvedBy}`);
        }
        catch (error) {
            logger_1.logger.error('', `Failed to approve response ${responseId}: ${error}`);
            throw error;
        }
    }
    async publishResponse(responseId) {
        try {
            // In a real implementation, this would publish to the actual platform
            // For now, we'll just update the status
            await database_handler_1.databaseHandler.prisma.aIResponseDraft.update({
                where: { id: responseId },
                data: {
                    status: 'PUBLISHED'
                }
            });
            logger_1.logger.info(`Response ${responseId} published`);
        }
        catch (error) {
            logger_1.logger.error('', `Failed to publish response ${responseId}: ${error}`);
            throw error;
        }
    }
    async getCommentsByProject(videoProjectId) {
        try {
            const comments = await database_handler_1.databaseHandler.prisma.userComment.findMany({
                where: { videoProjectId },
                include: { aiResponseDrafts: true }
            });
            return comments.map(this.mapDatabaseCommentToComment);
        }
        catch (error) {
            logger_1.logger.error('', `Failed to get comments for project ${videoProjectId}: ${error}`);
            return [];
        }
    }
    async getResponsesByComment(commentId) {
        try {
            const responses = await database_handler_1.databaseHandler.prisma.aIResponseDraft.findMany({
                where: { userCommentId: commentId },
                include: { userComment: true }
            });
            return responses.map(this.mapDatabaseResponseToCommentResponse);
        }
        catch (error) {
            logger_1.logger.error('', `Failed to get responses for comment ${commentId}: ${error}`);
            return [];
        }
    }
    async getPendingResponses() {
        try {
            const responses = await database_handler_1.databaseHandler.getPendingResponseDrafts();
            return responses.map(this.mapDatabaseResponseToCommentResponse);
        }
        catch (error) {
            logger_1.logger.error('', `Failed to get pending responses: ${error}`);
            return [];
        }
    }
    async getUnprocessedComments(videoProjectId) {
        try {
            const whereClause = videoProjectId ? { videoProjectId } : {};
            const comments = await database_handler_1.databaseHandler.prisma.userComment.findMany({
                where: {
                    ...whereClause,
                    aiResponseDrafts: {
                        none: {} // Comments without any AI response drafts
                    }
                },
                include: { aiResponseDrafts: true }
            });
            return comments.map(this.mapDatabaseCommentToComment);
        }
        catch (error) {
            logger_1.logger.error('', `Failed to get unprocessed comments: ${error}`);
            return [];
        }
    }
    async markCommentAsSpam(commentId) {
        await database_handler_1.databaseHandler.prisma.userComment.update({
            where: { id: commentId },
            data: { text: `[SPAM] ${await this.getCommentText(commentId)}` }
        });
    }
    async markCommentAsProcessed(commentId) {
        // Mark as processed by ensuring it has at least one response draft
        logger_1.logger.info(`Comment ${commentId} marked as processed`);
    }
    async getCommentText(commentId) {
        try {
            const comment = await database_handler_1.databaseHandler.prisma.userComment.findUnique({
                where: { id: commentId }
            });
            return comment?.text || '';
        }
        catch {
            return '';
        }
    }
    mapDatabaseCommentToComment(dbComment) {
        return {
            id: dbComment.id,
            videoProjectId: dbComment.videoProjectId,
            platform: dbComment.platform || 'youtube',
            platformCommentId: dbComment.commentId || dbComment.id,
            authorName: dbComment.author,
            authorId: dbComment.author,
            content: dbComment.text,
            sentiment: 'neutral',
            language: 'en',
            createdAt: dbComment.createdAt,
            updatedAt: dbComment.updatedAt,
            isReported: false,
            isSpam: dbComment.text?.includes('[SPAM]') || false,
            engagement: {
                likes: 0,
                replies: 0
            }
        };
    }
    mapDatabaseResponseToCommentResponse(dbResponse) {
        return {
            id: dbResponse.id,
            commentId: dbResponse.userCommentId,
            content: dbResponse.draftText,
            tone: 'friendly',
            language: 'en',
            status: dbResponse.status?.toLowerCase() || 'draft',
            aiGenerated: true,
            generatedAt: dbResponse.createdAt,
            approvedAt: dbResponse.approvedAt,
            publishedAt: dbResponse.publishedAt,
            metadata: {
                aiProvider: 'gemini',
                model: 'gemini-1.5-pro',
                confidence: 0.85,
                keywords: []
            }
        };
    }
    getDefaultAnalysis(comment) {
        return {
            sentiment: 'neutral',
            topics: [],
            intent: 'question',
            language: 'en',
            toxicity: 0,
            requiresResponse: true,
            urgency: 'low',
            suggestedResponseTone: 'friendly'
        };
    }
}
exports.CommentManagementSystem = CommentManagementSystem;
exports.commentManagementSystem = new CommentManagementSystem();
