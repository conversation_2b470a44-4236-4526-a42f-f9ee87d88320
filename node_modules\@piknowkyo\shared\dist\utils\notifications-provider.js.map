{"version": 3, "file": "notifications-provider.js", "sourceRoot": "", "sources": ["../../src/utils/notifications-provider.ts"], "names": [], "mappings": ";;;AAAA,qCAAkC;AAwClC,MAAa,qBAAqB;IAQhC;QAPQ,WAAM,GAAuB;YACnC,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE;YACzB,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;YACpC,KAAK,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE;YACzC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE;SAC5C,CAAC;QAGA,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,kBAAkB;QACxB,eAAM,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAEtD,gDAAgD;QAChD,IAAI,CAAC,MAAM,GAAG;YACZ,KAAK,EAAE;gBACL,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM;gBAC3D,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;oBAC5B,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS;oBAC3B,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC;oBAC9C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,MAAM;oBAC1C,IAAI,EAAE;wBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;wBACjC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;qBAClC;iBACF,CAAC,CAAC,CAAC,SAAS;aACd;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,KAAK,MAAM;gBAC7D,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;aACnC;YACD,KAAK,EAAE;gBACL,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM;gBAC3D,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;aAChD;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,KAAK,MAAM;gBAC7D,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,EAAE;aAClD;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,YAAoD;QACzE,MAAM,gBAAgB,GAAiB;YACrC,GAAG,YAAY;YACf,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,yBAAyB,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC;QAE/D,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,QAAQ,GAAoB,EAAE,CAAC;YAErC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;gBAC/B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,CAAC;gBAC/B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAC9D,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;gBACjC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,YAA0B;QAC5D,4DAA4D;QAC5D,eAAM,CAAC,IAAI,CAAC,4BAA4B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,YAA0B;QAC9D,uDAAuD;QACvD,eAAM,CAAC,IAAI,CAAC,8BAA8B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;IAClE,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,YAA0B;QAC5D,yDAAyD;QACzD,eAAM,CAAC,IAAI,CAAC,4BAA4B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,YAA0B;QAC9D,6DAA6D;QAC7D,eAAM,CAAC,IAAI,CAAC,8BAA8B,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,SAAiB,EACjB,MAAc,EACd,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,uBAAuB;YAC9B,OAAO,EAAE,WAAW,SAAS,sBAAsB,MAAM,EAAE;YAC3D,IAAI,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;YAC3B,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,KAAY,EACZ,OAAgB,EAChB,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,gBAAgB;YACvB,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,EAAE;YAC3D,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,OAAO,EAAE;YACrC,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,uBAAuB,CAC3B,KAAa,EACb,OAAe,EACf,MAAe;QAEf,MAAM,IAAI,CAAC,gBAAgB,CAAC;YAC1B,IAAI,EAAE,SAAS;YACf,KAAK;YACL,OAAO;YACP,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAEO,UAAU;QAChB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC1E,CAAC;IAED,YAAY,CAAC,SAAsC;QACjD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;QAC/C,eAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAED,SAAS;QACP,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF;AAxJD,sDAwJC;AAED,4BAA4B;AACf,QAAA,qBAAqB,GAAG,IAAI,qBAAqB,EAAE,CAAC;AACjE,kBAAe,6BAAqB,CAAC"}