import { logger } from './logger';
import { AIProvider, AIProviderType } from '../types/ai-provider';

export interface EnhancedAIProviderConfig {
  id: string;
  name: string;
  type: AIProviderType;
  apiKey: string;
  baseUrl?: string;
  models: string[];
  defaultModel: string;
  isActive: boolean;
}

export interface TaskAIConfig {
  taskType: string;
  providerId: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  provider: string;
}

export class EnhancedAIProvider {
  private providers: Map<string, EnhancedAIProviderConfig> = new Map();
  private taskConfigs: Map<string, TaskAIConfig> = new Map();

  constructor() {
    this.initializeProviders();
  }

  private initializeProviders() {
    // Initialize with default providers
    logger.info('Initializing Enhanced AI Provider...');
  }

  async addProvider(config: EnhancedAIProviderConfig): Promise<void> {
    this.providers.set(config.id, config);
    logger.info(`Added AI provider: ${config.name} (${config.type})`);
  }

  async removeProvider(providerId: string): Promise<void> {
    this.providers.delete(providerId);
    logger.info(`Removed AI provider: ${providerId}`);
  }

  async getProvider(providerId: string): Promise<EnhancedAIProviderConfig | undefined> {
    return this.providers.get(providerId);
  }

  async listProviders(): Promise<EnhancedAIProviderConfig[]> {
    return Array.from(this.providers.values());
  }

  async setTaskConfig(taskType: string, config: TaskAIConfig): Promise<void> {
    this.taskConfigs.set(taskType, config);
    logger.info(`Set task config for: ${taskType}`);
  }

  async getTaskConfig(taskType: string): Promise<TaskAIConfig | undefined> {
    return this.taskConfigs.get(taskType);
  }

  async generateText(
    prompt: string,
    taskType?: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      model?: string;
      providerId?: string;
    }
  ): Promise<AIResponse> {
    try {
      // Get task configuration
      const taskConfig = taskType ? await this.getTaskConfig(taskType) : undefined;
      
      // Determine which provider to use
      const providerId = options?.providerId || taskConfig?.providerId || 'default';
      const provider = await this.getProvider(providerId);
      
      if (!provider) {
        throw new Error(`Provider ${providerId} not found`);
      }

      // For now, return a mock response
      // In a real implementation, this would call the actual AI provider
      const response: AIResponse = {
        content: `Mock response for prompt: ${prompt.substring(0, 50)}...`,
        usage: {
          promptTokens: prompt.length / 4,
          completionTokens: 100,
          totalTokens: (prompt.length / 4) + 100
        },
        model: options?.model || taskConfig?.model || provider.defaultModel,
        provider: provider.name
      };

      logger.info(`Generated text using ${provider.name} (${response.model})`);
      return response;
    } catch (error) {
      logger.error('Failed to generate text:', error);
      throw error;
    }
  }

  async testProvider(providerId: string): Promise<boolean> {
    try {
      const provider = await this.getProvider(providerId);
      if (!provider) {
        return false;
      }

      // Test with a simple prompt
      await this.generateText('Test prompt', undefined, { providerId });
      return true;
    } catch (error) {
      logger.error(`Provider test failed for ${providerId}:`, error);
      return false;
    }
  }
}

// Export singleton instance
export const enhancedAIProvider = new EnhancedAIProvider();
export default enhancedAIProvider;
