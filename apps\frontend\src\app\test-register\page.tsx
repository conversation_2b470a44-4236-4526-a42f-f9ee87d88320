'use client';

import { useState } from 'react';
import { api } from '@/lib/api';

export default function TestRegisterPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [result, setResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const handleDirectApiTest = async () => {
    setIsLoading(true);
    setResult('Testing...');
    
    try {
      const response = await api.post('/auth/register', {
        email: `test${Date.now()}@example.com`,
        password: 'Test123!',
        name: 'Test User'
      });
      
      setResult(`✅ Success! Status: ${response.status}\nData: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error: any) {
      setResult(`❌ Error: ${error.message}\nResponse: ${JSON.stringify(error.response?.data, null, 2)}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormTest = async () => {
    if (!email || !password || !name) {
      setResult('❌ Please fill all fields');
      return;
    }

    setIsLoading(true);
    setResult('Testing form submission...');
    
    try {
      const response = await api.post('/auth/register', {
        email,
        password,
        name
      });
      
      setResult(`✅ Form Success! Status: ${response.status}\nData: ${JSON.stringify(response.data, null, 2)}`);
    } catch (error: any) {
      setResult(`❌ Form Error: ${error.message}\nResponse: ${JSON.stringify(error.response?.data, null, 2)}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <h2 className="text-2xl font-bold text-center mb-6">Test Registration</h2>
          
          <div className="space-y-4">
            <button
              onClick={handleDirectApiTest}
              disabled={isLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              Test Direct API Call
            </button>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4">Test with Form Data</h3>
              
              <div className="space-y-3">
                <input
                  type="text"
                  placeholder="Name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded"
                />
                <input
                  type="email"
                  placeholder="Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded"
                />
                <input
                  type="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded"
                />
                <button
                  onClick={handleFormTest}
                  disabled={isLoading}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 disabled:opacity-50"
                >
                  Test Form Submission
                </button>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-2">Result:</h3>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-96 whitespace-pre-wrap">
                {result || 'No test run yet'}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
