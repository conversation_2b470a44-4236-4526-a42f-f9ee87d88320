'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface AIProvider {
  id: string;
  name: string;
  type: string;
  isActive: boolean;
  isCustom: boolean;
  baseUrl?: string;
  createdAt: string;
}

interface AIProviderType {
  value: string;
  label: string;
  description: string;
}

export default function AIProvidersPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [providerTypes, setProviderTypes] = useState<AIProviderType[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newProvider, setNewProvider] = useState({
    name: '',
    type: 'GOOGLE',
    baseUrl: '',
    apiKeys: ['']
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      const [providersRes, typesRes] = await Promise.all([
        fetch('/api/ai-providers'),
        fetch('/api/ai-providers/types')
      ]);

      if (providersRes.ok) {
        const providersData = await providersRes.json();
        setProviders(providersData.data || []);
      }

      if (typesRes.ok) {
        const typesData = await typesRes.json();
        setProviderTypes(typesData.data || []);
      }
    } catch (error) {
      console.error('Error loading AI providers:', error);
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleAddProvider = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/ai-providers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newProvider),
      });

      if (response.ok) {
        await loadData();
        setShowAddForm(false);
        setNewProvider({ name: '', type: 'GOOGLE', baseUrl: '', apiKeys: [''] });
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to add provider'}`);
      }
    } catch (error) {
      console.error('Error adding provider:', error);
      alert('Failed to add provider. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleToggleProvider = async (providerId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/ai-providers/${providerId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !isActive }),
      });

      if (response.ok) {
        await loadData();
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to update provider'}`);
      }
    } catch (error) {
      console.error('Error updating provider:', error);
      alert('Failed to update provider. Please try again.');
    }
  };

  const addApiKeyField = () => {
    setNewProvider(prev => ({
      ...prev,
      apiKeys: [...prev.apiKeys, '']
    }));
  };

  const updateApiKey = (index: number, value: string) => {
    setNewProvider(prev => ({
      ...prev,
      apiKeys: prev.apiKeys.map((key, i) => i === index ? value : key)
    }));
  };

  const removeApiKey = (index: number) => {
    setNewProvider(prev => ({
      ...prev,
      apiKeys: prev.apiKeys.filter((_, i) => i !== index)
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-wide py-8">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI Providers</h1>
            <p className="mt-2 text-gray-600">
              Configure AI providers for content generation
            </p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary"
          >
            Add Provider
          </button>
        </div>

        {isLoadingData ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div className="space-y-6">
            {providers.length === 0 ? (
              <div className="card">
                <div className="card-body text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No AI providers configured</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Get started by adding your first AI provider.
                  </p>
                </div>
              </div>
            ) : (
              providers.map(provider => (
                <div key={provider.id} className="card">
                  <div className="card-body">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">{provider.name}</h3>
                        <p className="text-sm text-gray-500">{provider.type}</p>
                        {provider.baseUrl && (
                          <p className="text-xs text-gray-400 mt-1">{provider.baseUrl}</p>
                        )}
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          provider.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {provider.isActive ? 'Active' : 'Inactive'}
                        </span>
                        <button
                          onClick={() => handleToggleProvider(provider.id, provider.isActive)}
                          className={`btn-sm ${
                            provider.isActive ? 'btn-outline' : 'btn-primary'
                          }`}
                        >
                          {provider.isActive ? 'Disable' : 'Enable'}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
              <form onSubmit={handleAddProvider}>
                <div className="p-6">
                  <h2 className="text-lg font-medium text-gray-900 mb-4">Add AI Provider</h2>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Provider Name
                      </label>
                      <input
                        type="text"
                        required
                        className="input-field"
                        value={newProvider.name}
                        onChange={(e) => setNewProvider(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="My AI Provider"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Provider Type
                      </label>
                      <select
                        className="input-field"
                        value={newProvider.type}
                        onChange={(e) => setNewProvider(prev => ({ ...prev, type: e.target.value }))}
                      >
                        {providerTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {newProvider.type === 'CUSTOM' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Base URL
                        </label>
                        <input
                          type="url"
                          className="input-field"
                          value={newProvider.baseUrl}
                          onChange={(e) => setNewProvider(prev => ({ ...prev, baseUrl: e.target.value }))}
                          placeholder="https://api.example.com"
                        />
                      </div>
                    )}

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        API Keys
                      </label>
                      {newProvider.apiKeys.map((key, index) => (
                        <div key={index} className="flex gap-2 mb-2">
                          <input
                            type="password"
                            required
                            className="input-field flex-1"
                            value={key}
                            onChange={(e) => updateApiKey(index, e.target.value)}
                            placeholder="API Key"
                          />
                          {newProvider.apiKeys.length > 1 && (
                            <button
                              type="button"
                              onClick={() => removeApiKey(index)}
                              className="btn-outline btn-sm"
                            >
                              Remove
                            </button>
                          )}
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={addApiKeyField}
                        className="btn-outline btn-sm"
                      >
                        Add Another Key
                      </button>
                    </div>
                  </div>
                </div>

                <div className="px-6 py-4 bg-gray-50 flex gap-3">
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="btn-outline flex-1"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="btn-primary flex-1"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <LoadingSpinner size="sm" />
                        Adding...
                      </>
                    ) : (
                      'Add Provider'
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
