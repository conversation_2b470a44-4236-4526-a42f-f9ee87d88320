"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseHandler = exports.DatabaseHandler = void 0;
const client_1 = require("../client");
class DatabaseHandler {
    // User operations
    async getUser(id) {
        return await client_1.prisma.user.findUnique({
            where: { id }
        });
    }
    async getUserByEmail(email) {
        return await client_1.prisma.user.findUnique({
            where: { email }
        });
    }
    async createUser(data) {
        return await client_1.prisma.user.create({
            data
        });
    }
    // Video Project operations
    async getVideoProject(id) {
        return await client_1.prisma.videoProject.findUnique({
            where: { id },
            include: {
                user: true,
                multilingualVideos: true,
                comments: true
            }
        });
    }
    async getVideoProjects(status) {
        return await client_1.prisma.videoProject.findMany({
            where: status ? { status } : undefined,
            include: {
                user: true,
                multilingualVideos: true,
                comments: true
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    }
    async createVideoProject(data) {
        return await client_1.prisma.videoProject.create({
            data: {
                ...data,
                primaryLanguage: data.primaryLanguage || 'ENGLISH',
                status: data.status || 'IDEA_GENERATED'
            }
        });
    }
    async updateVideoProject(id, data) {
        return await client_1.prisma.videoProject.update({
            where: { id },
            data
        });
    }
    async updateVideoProjectStatus(id, status, metadata) {
        return await client_1.prisma.videoProject.update({
            where: { id },
            data: {
                status,
                metadata: metadata || null,
                updatedAt: new Date()
            }
        });
    }
    // AI Provider operations
    async getAIProvider(id) {
        return await client_1.prisma.aIProvider.findUnique({
            where: { id }
        });
    }
    async getAIProviders() {
        return await client_1.prisma.aIProvider.findMany({
            include: {
                apiKeys: true,
                models: true
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    }
    // Voice operations
    async getVoice(id) {
        return await client_1.prisma.voice.findUnique({
            where: { id }
        });
    }
    async getVoices() {
        return await client_1.prisma.voice.findMany({
            include: {
                provider: true
            },
            orderBy: {
                name: 'asc'
            }
        });
    }
    // Generic operations
    async create(table, data) {
        // This is a generic method for backward compatibility
        // In practice, use specific methods above
        const model = client_1.prisma[table];
        if (!model) {
            throw new Error(`Table ${table} not found`);
        }
        return await model.create({ data });
    }
    async findMany(table, options) {
        const model = client_1.prisma[table];
        if (!model) {
            throw new Error(`Table ${table} not found`);
        }
        return await model.findMany(options);
    }
    async findUnique(table, where) {
        const model = client_1.prisma[table];
        if (!model) {
            throw new Error(`Table ${table} not found`);
        }
        return await model.findUnique({ where });
    }
    async update(table, where, data) {
        const model = client_1.prisma[table];
        if (!model) {
            throw new Error(`Table ${table} not found`);
        }
        return await model.update({ where, data });
    }
    async delete(table, where) {
        const model = client_1.prisma[table];
        if (!model) {
            throw new Error(`Table ${table} not found`);
        }
        return await model.delete({ where });
    }
    // Health check
    async healthCheck() {
        try {
            await client_1.prisma.$queryRaw `SELECT 1`;
            return true;
        }
        catch (error) {
            console.error('Database health check failed:', error);
            return false;
        }
    }
    // Connection management
    async connect() {
        await client_1.prisma.$connect();
    }
    async disconnect() {
        await client_1.prisma.$disconnect();
    }
}
exports.DatabaseHandler = DatabaseHandler;
// Export singleton instance
exports.databaseHandler = new DatabaseHandler();
exports.default = exports.databaseHandler;
//# sourceMappingURL=database-handler.js.map