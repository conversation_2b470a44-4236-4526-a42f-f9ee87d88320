import { z } from 'zod';
export declare const createAIProviderSchema: z.ZodObject<{
    name: z.ZodString;
    type: z.Z<PERSON>Enum<["GOOGLE", "OPENROUTER", "GROQ", "MISTRAL", "CUSTOM"]>;
    apiKey: z.ZodString;
    baseUrl: z.Zod<PERSON>ptional<z.ZodString>;
    isActive: z.Z<PERSON>ptional<z.ZodBoolean>;
}, "strip", z.ZodType<PERSON>ny, {
    type: "GOOGLE" | "OPENROUTER" | "GROQ" | "MISTRAL" | "CUSTOM";
    name: string;
    apiKey: string;
    baseUrl?: string | undefined;
    isActive?: boolean | undefined;
}, {
    type: "GOOGLE" | "OPENROUTER" | "GROQ" | "MISTRAL" | "CUSTOM";
    name: string;
    apiKey: string;
    baseUrl?: string | undefined;
    isActive?: boolean | undefined;
}>;
export declare const updateAIProviderSchema: z.ZodObject<{
    name: z.ZodOptional<z.ZodString>;
    apiKey: z.ZodOptional<z.ZodString>;
    baseUrl: z.ZodOptional<z.ZodString>;
    isActive: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    name?: string | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    isActive?: boolean | undefined;
}, {
    name?: string | undefined;
    apiKey?: string | undefined;
    baseUrl?: string | undefined;
    isActive?: boolean | undefined;
}>;
export declare const testAIProviderSchema: z.ZodObject<{
    prompt: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    prompt?: string | undefined;
}, {
    prompt?: string | undefined;
}>;
export type CreateAIProviderInput = z.infer<typeof createAIProviderSchema>;
export type UpdateAIProviderInput = z.infer<typeof updateAIProviderSchema>;
export type TestAIProviderInput = z.infer<typeof testAIProviderSchema>;
//# sourceMappingURL=ai-provider-validators.d.ts.map