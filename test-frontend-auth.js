const fetch = require('node-fetch');

async function testFrontendAuth() {
  console.log('Testing frontend authentication flow...\n');

  try {
    // Test 1: Register a new user
    console.log('1. Testing user registration...');
    const registerResponse = await fetch('http://localhost:3001/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: `test${Date.now()}@example.com`,
        password: 'Password123!',
        name: 'Test User'
      })
    });

    if (!registerResponse.ok) {
      const errorData = await registerResponse.json();
      console.log('Registration failed:', errorData);
      return;
    }

    const registerData = await registerResponse.json();
    console.log('✅ Registration successful!');
    console.log('User ID:', registerData.data.user.id);
    console.log('Token received:', registerData.data.token ? 'Yes' : 'No');

    // Test 2: Login with the same user
    console.log('\n2. Testing user login...');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: registerData.data.user.email,
        password: 'Password123!'
      })
    });

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      console.log('Login failed:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');
    console.log('Token received:', loginData.data.token ? 'Yes' : 'No');

    // Test 3: Access protected route
    console.log('\n3. Testing protected route access...');
    const profileResponse = await fetch('http://localhost:3001/api/users/profile', {
      headers: {
        'Authorization': `Bearer ${loginData.data.token}`,
        'Content-Type': 'application/json',
      }
    });

    if (!profileResponse.ok) {
      const errorData = await profileResponse.json();
      console.log('Profile access failed:', errorData);
      return;
    }

    const profileData = await profileResponse.json();
    console.log('✅ Protected route access successful!');
    console.log('Profile data received:', profileData.data.email);

    console.log('\n🎉 All authentication tests passed!');
    console.log('\n📋 Summary:');
    console.log('- User registration: ✅ Working');
    console.log('- User login: ✅ Working');
    console.log('- JWT authentication: ✅ Working');
    console.log('- Protected routes: ✅ Working');
    console.log('\n🌐 Frontend URLs to test:');
    console.log('- Home: http://localhost:3000');
    console.log('- Login: http://localhost:3000/auth/login');
    console.log('- Register: http://localhost:3000/auth/register');
    console.log('- Dashboard: http://localhost:3000/dashboard');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

testFrontendAuth();
