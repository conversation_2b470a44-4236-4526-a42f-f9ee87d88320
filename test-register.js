const axios = require('axios');

async function testFrontendRegister() {
  try {
    console.log('Testing frontend registration flow...');

    // Test the frontend registration endpoint
    const response = await axios.post('http://localhost:3000/api/auth/register', {
      email: `frontend${Date.now()}@example.com`,
      password: 'Test123!',
      name: 'Frontend Test User'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('Frontend registration successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('Frontend registration error:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
    }
  }
}

async function testBackendRegister() {
  try {
    console.log('Testing backend registration...');

    const response = await axios.post('http://localhost:3001/api/auth/register', {
      email: `backend${Date.now()}@example.com`,
      password: 'Test123!',
      name: 'Backend Test User'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    console.log('Backend registration successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));

    // Test login with the same credentials
    console.log('\nTesting login...');
    const loginResponse = await axios.post('http://localhost:3001/api/auth/login', {
      email: response.data.data.user.email,
      password: 'Test123!'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Login successful!');
    console.log('Login Response:', JSON.stringify(loginResponse.data, null, 2));

  } catch (error) {
    console.error('Backend error:', error.response?.data || error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Headers:', error.response.headers);
    }
  }
}

async function runTests() {
  await testBackendRegister();
  console.log('\n' + '='.repeat(50) + '\n');
  await testFrontendRegister();
}

runTests();
