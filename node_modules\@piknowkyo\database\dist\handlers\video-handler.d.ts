import { VideoProject, Prisma } from '../generated';
export declare class VideoHandler {
    createVideoProject(data: {
        title: string;
        description?: string;
        userId: string;
        primaryLanguage?: string;
        status?: string;
        metadata?: string;
    }): Promise<VideoProject>;
    getVideoProject(id: string): Promise<VideoProject | null>;
    getVideoProjects(options?: {
        userId?: string;
        status?: string;
        skip?: number;
        take?: number;
        orderBy?: Prisma.VideoProjectOrderByWithRelationInput;
    }): Promise<VideoProject[]>;
    updateVideoProject(id: string, data: Partial<VideoProject>): Promise<VideoProject>;
    updateVideoProjectStatus(id: string, status: string, metadata?: string): Promise<VideoProject>;
    deleteVideoProject(id: string): Promise<VideoProject>;
    getVideoProjectsByStatus(status: string): Promise<VideoProject[]>;
    getVideoProjectCount(options?: {
        userId?: string;
        status?: string;
    }): Promise<number>;
    getUserVideoProjects(userId: string): Promise<VideoProject[]>;
}
export declare const videoHandler: VideoHandler;
export default videoHandler;
//# sourceMappingURL=video-handler.d.ts.map