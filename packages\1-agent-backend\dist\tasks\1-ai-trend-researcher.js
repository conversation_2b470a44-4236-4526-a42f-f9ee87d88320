"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiTrendResearcher = exports.AITrendResearcher = void 0;
const ai_provider_1 = require("../core/ai-provider");
const database_handler_1 = require("../core/database-handler");
const logger_1 = require("../core/logger");
const client_1 = require("@prisma/client");
class AITrendResearcher {
    async researchTrends() {
        try {
            logger_1.logger.info('Starting trend research...');
            // Mock trend research - in production, this would use real APIs
            const trendPrompt = `
        You are a social media trend researcher. Analyze current trending topics for short-form video content.
        Focus on topics that are:
        1. Currently trending on TikTok, YouTube Shorts, Instagram Reels
        2. Suitable for educational or entertaining content
        3. Have high engagement potential
        4. Can be explained in 60 seconds or less

        Return a JSON array of 5 trending topics with the following structure:
        {
          "topic": "Topic name",
          "popularity": 1-100,
          "keywords": ["keyword1", "keyword2"],
          "platforms": ["tiktok", "youtube", "instagram"],
          "audience": "target audience description",
          "contentType": "educational/entertainment/news/lifestyle"
        }
      `;
            const response = await ai_provider_1.aiProvider.generateText(trendPrompt);
            // Parse AI response (in production, you'd have better error handling)
            let trends;
            try {
                trends = JSON.parse(response);
            }
            catch (parseError) {
                logger_1.logger.error('', `Failed to parse trend data: ${parseError}`);
                // Fallback to mock data
                trends = this.getMockTrendData();
            }
            logger_1.logger.info(`Found ${trends.length} trending topics`);
            return trends;
        }
        catch (error) {
            logger_1.logger.error('', `Trend research failed: ${error}`);
            // Return mock data as fallback
            return this.getMockTrendData();
        }
    }
    async createVideoProjectFromTrend(trend, aiConfigurationId) {
        try {
            const title = `${trend.topic} - ${trend.contentType} video`;
            const description = `Video about ${trend.topic} targeting ${trend.audience}. Keywords: ${trend.keywords.join(', ')}`;
            const project = await database_handler_1.databaseHandler.createVideoProject(title, description, aiConfigurationId);
            logger_1.logger.info(`Created video project ${project.id} for trend: ${trend.topic}`);
            return project.id;
        }
        catch (error) {
            logger_1.logger.error('', `Failed to create video project from trend: ${error}`);
            throw error;
        }
    }
    async processTopTrends(maxProjects = 3) {
        try {
            const trends = await this.researchTrends();
            // Sort by popularity and take top trends
            const topTrends = trends
                .sort((a, b) => b.popularity - a.popularity)
                .slice(0, maxProjects);
            const projectIds = [];
            for (const trend of topTrends) {
                try {
                    const projectId = await this.createVideoProjectFromTrend(trend);
                    projectIds.push(projectId);
                }
                catch (error) {
                    logger_1.logger.error('', `Failed to create project for trend ${trend.topic}: ${error}`);
                }
            }
            logger_1.logger.info(`Created ${projectIds.length} video projects from trends`);
            return projectIds;
        }
        catch (error) {
            logger_1.logger.error('', `Failed to process top trends: ${error}`);
            throw error;
        }
    }
    getMockTrendData() {
        return [
            {
                topic: "AI Tools for Productivity",
                popularity: 95,
                keywords: ["AI", "productivity", "tools", "automation"],
                platforms: ["tiktok", "youtube", "linkedin"],
                audience: "professionals and students",
                contentType: "educational"
            },
            {
                topic: "Quick Healthy Recipes",
                popularity: 88,
                keywords: ["healthy", "recipes", "quick", "cooking"],
                platforms: ["tiktok", "instagram", "youtube"],
                audience: "health-conscious individuals",
                contentType: "lifestyle"
            },
            {
                topic: "Tech News Explained",
                popularity: 82,
                keywords: ["tech", "news", "explained", "simple"],
                platforms: ["youtube", "tiktok"],
                audience: "tech enthusiasts",
                contentType: "news"
            },
            {
                topic: "Life Hacks for Students",
                popularity: 79,
                keywords: ["life hacks", "students", "study", "tips"],
                platforms: ["tiktok", "instagram"],
                audience: "students and young adults",
                contentType: "educational"
            },
            {
                topic: "Funny Pet Moments",
                popularity: 75,
                keywords: ["pets", "funny", "animals", "cute"],
                platforms: ["tiktok", "instagram", "youtube"],
                audience: "pet lovers and general audience",
                contentType: "entertainment"
            }
        ];
    }
    async getProjectsNeedingTrendAnalysis() {
        try {
            const projects = await database_handler_1.databaseHandler.getVideoProjects(client_1.VideoProjectStatus.IDEA_GENERATED);
            return projects.map(p => p.id);
        }
        catch (error) {
            logger_1.logger.error('', `Failed to get projects needing trend analysis: ${error}`);
            return [];
        }
    }
}
exports.AITrendResearcher = AITrendResearcher;
exports.aiTrendResearcher = new AITrendResearcher();
