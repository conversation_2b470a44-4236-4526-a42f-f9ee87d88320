import { User, Prisma } from '../generated';
export declare class UserHandler {
    createUser(data: {
        email: string;
        password: string;
        name?: string;
    }): Promise<User>;
    getUserById(id: string): Promise<User | null>;
    getUserByEmail(email: string): Promise<User | null>;
    updateUser(id: string, data: Partial<User>): Promise<User>;
    deleteUser(id: string): Promise<User>;
    verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean>;
    updatePassword(id: string, newPassword: string): Promise<User>;
    getUsers(options?: {
        skip?: number;
        take?: number;
        orderBy?: Prisma.UserOrderByWithRelationInput;
    }): Promise<User[]>;
    getUserCount(): Promise<number>;
}
export declare const userHandler: UserHandler;
export default userHandler;
//# sourceMappingURL=user-handler.d.ts.map