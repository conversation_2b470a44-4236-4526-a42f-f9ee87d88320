'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface VideoProjectForm {
  title: string;
  description: string;
  primaryLanguage: string;
  targetLanguages: string[];
  content: string;
  settings: {
    duration: number;
    style: string;
  };
}

export default function NewVideoProjectPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [form, setForm] = useState<VideoProjectForm>({
    title: '',
    description: '',
    primaryLanguage: 'ENGLISH',
    targetLanguages: [],
    content: '',
    settings: {
      duration: 60,
      style: 'professional'
    }
  });

  const languages = [
    { value: 'ENGLISH', label: 'English' },
    { value: 'FRENCH', label: 'French' },
    { value: 'SPANISH', label: 'Spanish' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/videos', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      if (response.ok) {
        const result = await response.json();
        router.push(`/videos/${result.data.id}`);
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || 'Failed to create video project'}`);
      }
    } catch (error) {
      console.error('Error creating video project:', error);
      alert('Failed to create video project. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLanguageToggle = (language: string) => {
    setForm(prev => ({
      ...prev,
      targetLanguages: prev.targetLanguages.includes(language)
        ? prev.targetLanguages.filter(l => l !== language)
        : [...prev.targetLanguages, language]
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!user) {
    router.push('/auth/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container-wide py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Create New Video Project</h1>
          <p className="mt-2 text-gray-600">
            Set up your multilingual video content project
          </p>
        </div>

        <div className="max-w-2xl">
          <form onSubmit={handleSubmit} className="card">
            <div className="card-body space-y-6">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Project Title
                </label>
                <input
                  type="text"
                  id="title"
                  required
                  className="input-field"
                  value={form.title}
                  onChange={(e) => setForm(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter your video project title"
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  rows={3}
                  className="input-field"
                  value={form.description}
                  onChange={(e) => setForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your video project"
                />
              </div>

              <div>
                <label htmlFor="primaryLanguage" className="block text-sm font-medium text-gray-700 mb-2">
                  Primary Language
                </label>
                <select
                  id="primaryLanguage"
                  className="input-field"
                  value={form.primaryLanguage}
                  onChange={(e) => setForm(prev => ({ ...prev, primaryLanguage: e.target.value }))}
                >
                  {languages.map(lang => (
                    <option key={lang.value} value={lang.value}>
                      {lang.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Target Languages for Translation
                </label>
                <div className="space-y-2">
                  {languages.filter(lang => lang.value !== form.primaryLanguage).map(lang => (
                    <label key={lang.value} className="flex items-center">
                      <input
                        type="checkbox"
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        checked={form.targetLanguages.includes(lang.value)}
                        onChange={() => handleLanguageToggle(lang.value)}
                      />
                      <span className="ml-2 text-sm text-gray-700">{lang.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                  Content/Script
                </label>
                <textarea
                  id="content"
                  rows={6}
                  required
                  className="input-field"
                  value={form.content}
                  onChange={(e) => setForm(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Enter your video script or content outline"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                    Duration (seconds)
                  </label>
                  <input
                    type="number"
                    id="duration"
                    min="30"
                    max="300"
                    className="input-field"
                    value={form.settings.duration}
                    onChange={(e) => setForm(prev => ({
                      ...prev,
                      settings: { ...prev.settings, duration: parseInt(e.target.value) }
                    }))}
                  />
                </div>

                <div>
                  <label htmlFor="style" className="block text-sm font-medium text-gray-700 mb-2">
                    Video Style
                  </label>
                  <select
                    id="style"
                    className="input-field"
                    value={form.settings.style}
                    onChange={(e) => setForm(prev => ({
                      ...prev,
                      settings: { ...prev.settings, style: e.target.value }
                    }))}
                  >
                    <option value="professional">Professional</option>
                    <option value="casual">Casual</option>
                    <option value="educational">Educational</option>
                    <option value="entertainment">Entertainment</option>
                  </select>
                </div>
              </div>

              <div className="flex gap-4 pt-4">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="btn-outline flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary flex-1"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <LoadingSpinner size="sm" />
                      Creating...
                    </>
                  ) : (
                    'Create Project'
                  )}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
