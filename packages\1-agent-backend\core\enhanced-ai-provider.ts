import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger } from './logger';

export interface AIProviderConfig {
  id: string;
  name: string;
  type: 'gemini' | 'openrouter' | 'groq' | 'mistral';
  apiKey: string;
  baseUrl?: string;
  models: string[];
  defaultModel: string;
  isActive: boolean;
}

export interface TaskAIConfig {
  taskType: string;
  providerId: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  provider: string;
}

export class EnhancedAIProvider {
  private providers: Map<string, AIProviderConfig> = new Map();
  private taskConfigs: Map<string, TaskAIConfig> = new Map();

  constructor() {
    this.initializeDefaultProviders();
  }

  private initializeDefaultProviders() {
    // Google Gemini
    if (process.env.GOOGLE_AI_API_KEY) {
      this.providers.set('gemini', {
        id: 'gemini',
        name: 'Google Gemini',
        type: 'gemini',
        apiKey: process.env.GOOGLE_AI_API_KEY,
        models: ['gemini-1.5-pro', 'gemini-1.5-flash', 'gemini-1.0-pro'],
        defaultModel: 'gemini-1.5-pro',
        isActive: true
      });
    }

    // OpenRouter
    if (process.env.OPENROUTER_API_KEY) {
      this.providers.set('openrouter', {
        id: 'openrouter',
        name: 'OpenRouter',
        type: 'openrouter',
        apiKey: process.env.OPENROUTER_API_KEY,
        baseUrl: 'https://openrouter.ai/api/v1',
        models: [
          'anthropic/claude-3.5-sonnet',
          'meta-llama/llama-3.1-405b-instruct',
          'google/gemini-pro-1.5',
          'openai/gpt-4o'
        ],
        defaultModel: 'anthropic/claude-3.5-sonnet',
        isActive: true
      });
    }

    // Groq
    if (process.env.GROQ_API_KEY) {
      this.providers.set('groq', {
        id: 'groq',
        name: 'Groq',
        type: 'groq',
        apiKey: process.env.GROQ_API_KEY,
        baseUrl: 'https://api.groq.com/openai/v1',
        models: [
          'llama-3.1-405b-reasoning',
          'llama-3.1-70b-versatile',
          'llama-3.1-8b-instant',
          'mixtral-8x7b-32768'
        ],
        defaultModel: 'llama-3.1-70b-versatile',
        isActive: true
      });
    }

    // Mistral
    if (process.env.MISTRAL_API_KEY) {
      this.providers.set('mistral', {
        id: 'mistral',
        name: 'Mistral AI',
        type: 'mistral',
        apiKey: process.env.MISTRAL_API_KEY,
        baseUrl: 'https://api.mistral.ai/v1',
        models: [
          'mistral-large-latest',
          'mistral-medium-latest',
          'mistral-small-latest',
          'codestral-latest'
        ],
        defaultModel: 'mistral-large-latest',
        isActive: true
      });
    }
  }

  async generateText(prompt: string, taskType: string = 'default'): Promise<AIResponse> {
    const config = this.getTaskConfig(taskType);
    const provider = this.providers.get(config.providerId);

    if (!provider) {
      throw new Error(`Provider ${config.providerId} not found`);
    }

    try {
      switch (provider.type) {
        case 'gemini':
          return await this.generateWithGemini(prompt, provider, config);
        case 'openrouter':
        case 'groq':
        case 'mistral':
          return await this.generateWithOpenAI(prompt, provider, config);
        default:
          throw new Error(`Unsupported provider type: ${provider.type}`);
      }
    } catch (error) {
      logger.error('', `AI generation failed for ${taskType}: ${error}`);
      throw error;
    }
  }

  private async generateWithGemini(prompt: string, provider: AIProviderConfig, config: TaskAIConfig): Promise<AIResponse> {
    const genAI = new GoogleGenerativeAI(provider.apiKey);
    const model = genAI.getGenerativeModel({ model: config.model });

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    return {
      content: text,
      model: config.model,
      provider: provider.name,
      usage: {
        promptTokens: 0, // Gemini doesn't provide token counts
        completionTokens: 0,
        totalTokens: 0
      }
    };
  }

  private async generateWithOpenAI(prompt: string, provider: AIProviderConfig, config: TaskAIConfig): Promise<AIResponse> {
    const response = await fetch(`${provider.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${provider.apiKey}`,
        'Content-Type': 'application/json',
        ...(provider.type === 'openrouter' && {
          'HTTP-Referer': 'https://piknowkyo-generator.com',
          'X-Title': 'Piknowkyo Generator'
        })
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          ...(config.systemPrompt ? [{ role: 'system', content: config.systemPrompt }] : []),
          { role: 'user', content: prompt }
        ],
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 4000
      })
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }

    const data: any = await response.json();

    return {
      content: data.choices[0].message.content,
      model: config.model,
      provider: provider.name,
      usage: data.usage || { promptTokens: 0, completionTokens: 0, totalTokens: 0 }
    };
  }

  private getTaskConfig(taskType: string): TaskAIConfig {
    const config = this.taskConfigs.get(taskType);
    if (config) {
      return config;
    }

    // Default configuration
    const defaultProvider = Array.from(this.providers.values()).find(p => p.isActive);
    if (!defaultProvider) {
      throw new Error('No active AI providers configured');
    }

    return {
      taskType,
      providerId: defaultProvider.id,
      model: defaultProvider.defaultModel,
      temperature: 0.7,
      maxTokens: 4000
    };
  }

  setTaskConfig(taskType: string, config: TaskAIConfig): void {
    this.taskConfigs.set(taskType, config);
  }

  getProviders(): AIProviderConfig[] {
    return Array.from(this.providers.values());
  }

  addProvider(config: AIProviderConfig): void {
    this.providers.set(config.id, config);
  }

  async testProvider(providerId: string, model: string): Promise<{ success: boolean; error?: string; response?: string }> {
    try {
      const provider = this.providers.get(providerId);
      if (!provider) {
        return { success: false, error: 'Provider not found' };
      }

      const testConfig: TaskAIConfig = {
        taskType: 'test',
        providerId,
        model,
        temperature: 0.7,
        maxTokens: 100
      };

      const originalConfig = this.taskConfigs.get('test');
      this.taskConfigs.set('test', testConfig);

      const response = await this.generateText('Say "Hello, this is a test!" in a creative way.', 'test');

      if (originalConfig) {
        this.taskConfigs.set('test', originalConfig);
      } else {
        this.taskConfigs.delete('test');
      }

      return { success: true, response: response.content };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  async generateWithWebSearch(prompt: string, taskType: string = 'default'): Promise<AIResponse> {
    const searchEnhancedPrompt = `
You have access to real-time web search capabilities. When answering this request, you can search for current information from:
- X (Twitter) for trending topics and real-time discussions
- Facebook for social media trends and engagement
- YouTube for video content trends and popular topics
- TikTok for viral content and short-form video trends
- Reddit for community discussions and emerging topics
- Google Trends for search volume and trending queries

Original request: ${prompt}

Please provide a comprehensive response using current web data when relevant.
`;

    return await this.generateText(searchEnhancedPrompt, taskType);
  }
}

export const enhancedAIProvider = new EnhancedAIProvider();
