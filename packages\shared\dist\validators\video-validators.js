"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.videoProjectQuerySchema = exports.updateVideoProjectSchema = exports.createVideoProjectSchema = void 0;
const zod_1 = require("zod");
exports.createVideoProjectSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Title is required').max(200, 'Title too long'),
    description: zod_1.z.string().max(1000, 'Description too long').optional(),
    aiProviderId: zod_1.z.string().uuid('Invalid AI provider ID').optional(),
    voiceId: zod_1.z.string().uuid('Invalid voice ID').optional(),
    script: zod_1.z.string().max(10000, 'Script too long').optional(),
});
exports.updateVideoProjectSchema = zod_1.z.object({
    title: zod_1.z.string().min(1, 'Title is required').max(200, 'Title too long').optional(),
    description: zod_1.z.string().max(1000, 'Description too long').optional(),
    aiProviderId: zod_1.z.string().uuid('Invalid AI provider ID').optional(),
    voiceId: zod_1.z.string().uuid('Invalid voice ID').optional(),
    script: zod_1.z.string().max(10000, 'Script too long').optional(),
    status: zod_1.z.enum(['PENDING', 'PROCESSING', 'COMPLETED', 'ERROR']).optional(),
});
exports.videoProjectQuerySchema = zod_1.z.object({
    page: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: zod_1.z.string().regex(/^\d+$/).transform(Number).optional(),
    status: zod_1.z.enum(['PENDING', 'PROCESSING', 'COMPLETED', 'ERROR']).optional(),
    search: zod_1.z.string().max(100).optional(),
});
//# sourceMappingURL=video-validators.js.map