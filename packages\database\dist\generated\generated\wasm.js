
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  name: 'name',
  role: 'role',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.VideoProjectScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  primaryLanguage: 'primaryLanguage',
  status: 'status',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.MultilingualVideoScalarFieldEnum = {
  id: 'id',
  language: 'language',
  title: 'title',
  description: 'description',
  script: 'script',
  audioUrl: 'audioUrl',
  videoUrl: 'videoUrl',
  thumbnailUrl: 'thumbnailUrl',
  status: 'status',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  videoProjectId: 'videoProjectId'
};

exports.Prisma.AIProviderScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  baseUrl: 'baseUrl',
  isActive: 'isActive',
  isCustom: 'isCustom',
  config: 'config',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIProviderApiKeyScalarFieldEnum = {
  id: 'id',
  keyName: 'keyName',
  encryptedKey: 'encryptedKey',
  isActive: 'isActive',
  lastUsed: 'lastUsed',
  usageCount: 'usageCount',
  createdAt: 'createdAt',
  providerId: 'providerId'
};

exports.Prisma.AIModelScalarFieldEnum = {
  id: 'id',
  name: 'name',
  displayName: 'displayName',
  description: 'description',
  maxTokens: 'maxTokens',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  providerId: 'providerId'
};

exports.Prisma.TTSProviderScalarFieldEnum = {
  id: 'id',
  name: 'name',
  type: 'type',
  baseUrl: 'baseUrl',
  isActive: 'isActive',
  isCustom: 'isCustom',
  config: 'config',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TTSProviderApiKeyScalarFieldEnum = {
  id: 'id',
  keyName: 'keyName',
  encryptedKey: 'encryptedKey',
  isActive: 'isActive',
  lastUsed: 'lastUsed',
  usageCount: 'usageCount',
  createdAt: 'createdAt',
  providerId: 'providerId'
};

exports.Prisma.VoiceScalarFieldEnum = {
  id: 'id',
  voiceId: 'voiceId',
  name: 'name',
  language: 'language',
  gender: 'gender',
  style: 'style',
  previewUrl: 'previewUrl',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  providerId: 'providerId'
};

exports.Prisma.UserSettingsScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.UserAISettingsScalarFieldEnum = {
  id: 'id',
  taskType: 'taskType',
  temperature: 'temperature',
  maxTokens: 'maxTokens',
  customPrompt: 'customPrompt',
  isActive: 'isActive',
  userSettingsId: 'userSettingsId',
  aiProviderId: 'aiProviderId',
  modelName: 'modelName'
};

exports.Prisma.UserVoiceSettingsScalarFieldEnum = {
  id: 'id',
  language: 'language',
  speed: 'speed',
  pitch: 'pitch',
  volume: 'volume',
  isActive: 'isActive',
  userSettingsId: 'userSettingsId',
  ttsProviderId: 'ttsProviderId',
  voiceId: 'voiceId'
};

exports.Prisma.UserGeneralSettingsScalarFieldEnum = {
  id: 'id',
  defaultLanguage: 'defaultLanguage',
  enableAutoCreation: 'enableAutoCreation',
  creationSchedule: 'creationSchedule',
  maxVideosPerDay: 'maxVideosPerDay',
  enableNotifications: 'enableNotifications',
  notificationChannels: 'notificationChannels',
  contentOutputDir: 'contentOutputDir',
  defaultVideoDuration: 'defaultVideoDuration',
  userSettingsId: 'userSettingsId'
};

exports.Prisma.CommentScalarFieldEnum = {
  id: 'id',
  platform: 'platform',
  content: 'content',
  author: 'author',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  videoProjectId: 'videoProjectId'
};

exports.Prisma.CommentResponseScalarFieldEnum = {
  id: 'id',
  content: 'content',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  commentId: 'commentId'
};

exports.Prisma.ApiUsageScalarFieldEnum = {
  id: 'id',
  provider: 'provider',
  model: 'model',
  endpoint: 'endpoint',
  tokens: 'tokens',
  cost: 'cost',
  success: 'success',
  errorMessage: 'errorMessage',
  createdAt: 'createdAt',
  userId: 'userId'
};

exports.Prisma.SystemConfigScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  description: 'description',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  VideoProject: 'VideoProject',
  MultilingualVideo: 'MultilingualVideo',
  AIProvider: 'AIProvider',
  AIProviderApiKey: 'AIProviderApiKey',
  AIModel: 'AIModel',
  TTSProvider: 'TTSProvider',
  TTSProviderApiKey: 'TTSProviderApiKey',
  Voice: 'Voice',
  UserSettings: 'UserSettings',
  UserAISettings: 'UserAISettings',
  UserVoiceSettings: 'UserVoiceSettings',
  UserGeneralSettings: 'UserGeneralSettings',
  Comment: 'Comment',
  CommentResponse: 'CommentResponse',
  ApiUsage: 'ApiUsage',
  SystemConfig: 'SystemConfig'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
