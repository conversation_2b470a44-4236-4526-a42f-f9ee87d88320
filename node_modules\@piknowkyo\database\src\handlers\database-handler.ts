import { prisma } from '../client';
import { Video<PERSON>roject, User, AI<PERSON>rovider, Voice } from '../generated';

export class DatabaseHandler {
  // User operations
  async getUser(id: string): Promise<User | null> {
    return await prisma.user.findUnique({
      where: { id }
    });
  }

  async getUserByEmail(email: string): Promise<User | null> {
    return await prisma.user.findUnique({
      where: { email }
    });
  }

  async createUser(data: {
    email: string;
    password: string;
    name?: string;
  }): Promise<User> {
    return await prisma.user.create({
      data
    });
  }

  // Video Project operations
  async getVideoProject(id: string): Promise<VideoProject | null> {
    return await prisma.videoProject.findUnique({
      where: { id },
      include: {
        user: true,
        multilingualVideos: true,
        comments: true
      }
    });
  }

  async getVideoProjects(status?: string): Promise<VideoProject[]> {
    return await prisma.videoProject.findMany({
      where: status ? { status } : undefined,
      include: {
        user: true,
        multilingualVideos: true,
        comments: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async createVideoProject(data: {
    title: string;
    description?: string;
    userId: string;
    primaryLanguage?: string;
    status?: string;
    metadata?: string;
  }): Promise<VideoProject> {
    return await prisma.videoProject.create({
      data: {
        ...data,
        primaryLanguage: data.primaryLanguage || 'ENGLISH',
        status: data.status || 'IDEA_GENERATED'
      }
    });
  }

  async updateVideoProject(id: string, data: Partial<VideoProject>): Promise<VideoProject> {
    return await prisma.videoProject.update({
      where: { id },
      data
    });
  }

  async updateVideoProjectStatus(
    id: string,
    status: string,
    metadata?: string
  ): Promise<VideoProject> {
    return await prisma.videoProject.update({
      where: { id },
      data: {
        status,
        metadata: metadata || null,
        updatedAt: new Date()
      }
    });
  }

  // AI Provider operations
  async getAIProvider(id: string): Promise<AIProvider | null> {
    return await prisma.aIProvider.findUnique({
      where: { id }
    });
  }

  async getAIProviders(): Promise<AIProvider[]> {
    return await prisma.aIProvider.findMany({
      include: {
        apiKeys: true,
        models: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  // Voice operations
  async getVoice(id: string): Promise<Voice | null> {
    return await prisma.voice.findUnique({
      where: { id }
    });
  }

  async getVoices(): Promise<Voice[]> {
    return await prisma.voice.findMany({
      include: {
        provider: true
      },
      orderBy: {
        name: 'asc'
      }
    });
  }

  // Generic operations
  async create(table: string, data: any): Promise<any> {
    // This is a generic method for backward compatibility
    // In practice, use specific methods above
    const model = (prisma as any)[table];
    if (!model) {
      throw new Error(`Table ${table} not found`);
    }
    return await model.create({ data });
  }

  async findMany(table: string, options?: any): Promise<any[]> {
    const model = (prisma as any)[table];
    if (!model) {
      throw new Error(`Table ${table} not found`);
    }
    return await model.findMany(options);
  }

  async findUnique(table: string, where: any): Promise<any> {
    const model = (prisma as any)[table];
    if (!model) {
      throw new Error(`Table ${table} not found`);
    }
    return await model.findUnique({ where });
  }

  async update(table: string, where: any, data: any): Promise<any> {
    const model = (prisma as any)[table];
    if (!model) {
      throw new Error(`Table ${table} not found`);
    }
    return await model.update({ where, data });
  }

  async delete(table: string, where: any): Promise<any> {
    const model = (prisma as any)[table];
    if (!model) {
      throw new Error(`Table ${table} not found`);
    }
    return await model.delete({ where });
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Database health check failed:', error);
      return false;
    }
  }

  // Connection management
  async connect(): Promise<void> {
    await prisma.$connect();
  }

  async disconnect(): Promise<void> {
    await prisma.$disconnect();
  }
}

// Export singleton instance
export const databaseHandler = new DatabaseHandler();
export default databaseHandler;
