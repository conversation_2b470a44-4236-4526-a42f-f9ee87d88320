import { prisma } from '../client';
import { VideoProject, Prisma } from '../generated';

export class VideoHandler {
  async createVideoProject(data: {
    title: string;
    description?: string;
    userId: string;
    primaryLanguage?: string;
    status?: string;
    metadata?: string;
  }): Promise<VideoProject> {
    return await prisma.videoProject.create({
      data: {
        ...data,
        primaryLanguage: data.primaryLanguage || 'ENGLISH',
        status: data.status || 'IDEA_GENERATED'
      }
    });
  }

  async getVideoProject(id: string): Promise<VideoProject | null> {
    return await prisma.videoProject.findUnique({
      where: { id },
      include: {
        user: true,
        multilingualVideos: true,
        comments: true
      }
    });
  }

  async getVideoProjects(options?: {
    userId?: string;
    status?: string;
    skip?: number;
    take?: number;
    orderBy?: Prisma.VideoProjectOrderByWithRelationInput;
  }): Promise<VideoProject[]> {
    return await prisma.videoProject.findMany({
      where: {
        userId: options?.userId,
        status: options?.status
      },
      skip: options?.skip,
      take: options?.take,
      orderBy: options?.orderBy || { createdAt: 'desc' },
      include: {
        user: true,
        multilingualVideos: true,
        comments: true
      }
    });
  }

  async updateVideoProject(id: string, data: Partial<VideoProject>): Promise<VideoProject> {
    return await prisma.videoProject.update({
      where: { id },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  async updateVideoProjectStatus(
    id: string,
    status: string,
    metadata?: string
  ): Promise<VideoProject> {
    return await prisma.videoProject.update({
      where: { id },
      data: {
        status,
        metadata: metadata || null,
        updatedAt: new Date()
      }
    });
  }

  async deleteVideoProject(id: string): Promise<VideoProject> {
    return await prisma.videoProject.delete({
      where: { id }
    });
  }

  async getVideoProjectsByStatus(status: string): Promise<VideoProject[]> {
    return await prisma.videoProject.findMany({
      where: { status },
      include: {
        user: true,
        multilingualVideos: true,
        comments: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    });
  }

  async getVideoProjectCount(options?: {
    userId?: string;
    status?: string;
  }): Promise<number> {
    return await prisma.videoProject.count({
      where: {
        userId: options?.userId,
        status: options?.status
      }
    });
  }

  async getUserVideoProjects(userId: string): Promise<VideoProject[]> {
    return await prisma.videoProject.findMany({
      where: { userId },
      include: {
        user: true,
        multilingualVideos: true,
        comments: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }
}

export const videoHandler = new VideoHandler();
export default videoHandler;
