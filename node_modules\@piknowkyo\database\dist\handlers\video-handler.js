"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.videoHandler = exports.VideoHandler = void 0;
const client_1 = require("../client");
class VideoHandler {
    async createVideoProject(data) {
        return await client_1.prisma.videoProject.create({
            data: {
                ...data,
                primaryLanguage: data.primaryLanguage || 'ENGLISH',
                status: data.status || 'IDEA_GENERATED'
            }
        });
    }
    async getVideoProject(id) {
        return await client_1.prisma.videoProject.findUnique({
            where: { id },
            include: {
                user: true,
                multilingualVideos: true,
                comments: true
            }
        });
    }
    async getVideoProjects(options) {
        return await client_1.prisma.videoProject.findMany({
            where: {
                userId: options?.userId,
                status: options?.status
            },
            skip: options?.skip,
            take: options?.take,
            orderBy: options?.orderBy || { createdAt: 'desc' },
            include: {
                user: true,
                multilingualVideos: true,
                comments: true
            }
        });
    }
    async updateVideoProject(id, data) {
        return await client_1.prisma.videoProject.update({
            where: { id },
            data: {
                ...data,
                updatedAt: new Date()
            }
        });
    }
    async updateVideoProjectStatus(id, status, metadata) {
        return await client_1.prisma.videoProject.update({
            where: { id },
            data: {
                status,
                metadata: metadata || null,
                updatedAt: new Date()
            }
        });
    }
    async deleteVideoProject(id) {
        return await client_1.prisma.videoProject.delete({
            where: { id }
        });
    }
    async getVideoProjectsByStatus(status) {
        return await client_1.prisma.videoProject.findMany({
            where: { status },
            include: {
                user: true,
                multilingualVideos: true,
                comments: true
            },
            orderBy: {
                createdAt: 'asc'
            }
        });
    }
    async getVideoProjectCount(options) {
        return await client_1.prisma.videoProject.count({
            where: {
                userId: options?.userId,
                status: options?.status
            }
        });
    }
    async getUserVideoProjects(userId) {
        return await client_1.prisma.videoProject.findMany({
            where: { userId },
            include: {
                user: true,
                multilingualVideos: true,
                comments: true
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
    }
}
exports.VideoHandler = VideoHandler;
exports.videoHandler = new VideoHandler();
exports.default = exports.videoHandler;
//# sourceMappingURL=video-handler.js.map