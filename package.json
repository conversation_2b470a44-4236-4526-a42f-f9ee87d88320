{"name": "piknowkyo-generator", "version": "1.0.0", "description": "Professional multilingual video content generation system", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=apps/backend", "dev:frontend": "npm run dev --workspace=apps/frontend", "build": "npm run build --workspaces", "build:backend": "npm run build --workspace=apps/backend", "build:frontend": "npm run build --workspace=apps/frontend", "start": "npm run start --workspace=apps/backend", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:backend": "jest --selectProjects backend", "test:frontend": "jest --selectProjects frontend", "test:shared": "jest --selectProjects shared", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "npm run type-check:backend && npm run type-check:frontend && npm run type-check:shared", "type-check:backend": "npm run type-check --workspace=apps/backend", "type-check:frontend": "npm run type-check --workspace=apps/frontend", "type-check:shared": "npm run type-check --workspace=packages/shared", "db:setup": "npm run db:generate && npm run db:migrate && npm run db:seed", "db:generate": "npm run db:generate --workspace=apps/backend", "db:migrate": "npm run db:migrate --workspace=apps/backend", "db:seed": "npm run db:seed --workspace=apps/backend", "db:studio": "npm run db:studio --workspace=apps/backend", "clean": "rimraf node_modules apps/*/node_modules packages/*/node_modules apps/*/dist apps/*/.next packages/*/dist coverage", "clean:cache": "npm run clean && rimraf .eslintcache .next/cache", "prepare": "husky install", "pre-commit": "lint-staged", "validate": "npm run lint && npm run type-check && npm run test:coverage", "postinstall": "npm run db:generate"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/bcryptjs": "^2.4.0", "@types/compression": "^1.7.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "husky": "^8.0.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "rimraf": "^5.0.0", "supertest": "^6.3.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["video-generation", "multilingual", "ai", "content-creation", "french", "english", "spanish"], "author": "Your Name", "license": "MIT", "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "dependencies": {"node-fetch": "^3.3.2"}}