{"name": "@piknowkyo/database", "version": "1.0.0", "description": "Database client and utilities for Piknowkyo Generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && cp -r src/generated dist/generated", "dev": "tsc --watch", "clean": "rm -rf dist", "db:generate": "prisma generate --schema=../../database/schema.prisma", "db:migrate": "prisma migrate dev --schema=../../database/schema.prisma", "db:deploy": "prisma migrate deploy --schema=../../database/schema.prisma", "db:reset": "prisma migrate reset --schema=../../database/schema.prisma --force", "db:seed": "tsx src/seed.ts", "db:studio": "prisma studio --schema=../../database/schema.prisma", "lint": "eslint src --ext .ts", "test": "jest"}, "dependencies": {"@prisma/client": "^5.0.0", "bcryptjs": "^2.4.3", "crypto-js": "^4.1.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/crypto-js": "^4.1.1", "@types/node": "^20.0.0", "prisma": "^5.0.0", "tsx": "^3.12.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./client": {"types": "./dist/client.d.ts", "default": "./dist/client.js"}, "./handlers": {"types": "./dist/handlers/index.d.ts", "default": "./dist/handlers/index.js"}, "./generated": {"types": "./src/generated/index.d.ts", "default": "./src/generated/index.js"}}}