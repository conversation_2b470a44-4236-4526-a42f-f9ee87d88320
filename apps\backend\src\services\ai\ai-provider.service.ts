import axios, { AxiosResponse } from 'axios';
import CryptoJ<PERSON> from 'crypto-js';
import { prisma } from '@piknowkyo/database/client';
import { logger } from '../../config/logger';
import { createError } from '../../middleware/error-handler';
import { ERROR_CODES, AI_PROVIDERS } from '@piknowkyo/shared/constants';
import {
  AIProvider,
  AIModel,
  ChatCompletionRequest,
  ChatCompletionResponse,
  ModelFetchResponse,
  AIProviderType
} from '@piknowkyo/shared/types';

export class AIProviderService {
  private encryptionKey: string;

  constructor() {
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-key-change-in-production';
  }

  // Encrypt API key
  private encryptApiKey(apiKey: string): string {
    return CryptoJS.AES.encrypt(apiKey, this.encryptionKey).toString();
  }

  // Decrypt API key
  private decryptApiKey(encryptedKey: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedKey, this.encryptionKey);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  // Get all active AI providers
  async getProviders(): Promise<AIProvider[]> {
    return await prisma.aIProvider.findMany({
      where: { isActive: true },
      include: {
        models: {
          where: { isActive: true }
        },
        apiKeys: {
          where: { isActive: true },
          select: {
            id: true,
            keyName: true,
            isActive: true,
            lastUsed: true,
            usageCount: true
          }
        }
      }
    });
  }

  // Get provider by ID
  async getProvider(id: string): Promise<AIProvider | null> {
    return await prisma.aIProvider.findUnique({
      where: { id },
      include: {
        models: {
          where: { isActive: true }
        },
        apiKeys: {
          where: { isActive: true },
          select: {
            id: true,
            keyName: true,
            isActive: true,
            lastUsed: true,
            usageCount: true
          }
        }
      }
    });
  }

  // Create or update AI provider
  async upsertProvider(data: {
    name: string;
    type: AIProviderType;
    baseUrl?: string;
    isCustom?: boolean;
    config?: Record<string, any>;
    apiKeys?: string[];
  }): Promise<AIProvider> {
    const { apiKeys, config, ...providerData } = data;

    // Create or update provider
    const provider = await prisma.aIProvider.upsert({
      where: { name: data.name },
      create: {
        ...providerData,
        isCustom: data.isCustom || false,
        config: config ? JSON.stringify(config) : null
      },
      update: {
        ...providerData,
        config: config ? JSON.stringify(config) : null,
        updatedAt: new Date()
      }
    });

    // Update API keys if provided
    if (apiKeys && apiKeys.length > 0) {
      // Remove existing keys
      await prisma.aIProviderApiKey.deleteMany({
        where: { providerId: provider.id }
      });

      // Add new keys
      const keyData = apiKeys.map((key, index) => ({
        providerId: provider.id,
        keyName: `key_${index + 1}`,
        encryptedKey: this.encryptApiKey(key)
      }));

      await prisma.aIProviderApiKey.createMany({
        data: keyData
      });
    }

    return provider;
  }

  // Fetch models from provider API
  async fetchModels(providerId: string): Promise<AIModel[]> {
    const provider = await this.getProvider(providerId);
    if (!provider) {
      throw createError('Provider not found', 404, ERROR_CODES.NOT_FOUND);
    }

    const apiKey = await this.getActiveApiKey(providerId);
    if (!apiKey) {
      throw createError('No active API key found for provider', 400, ERROR_CODES.AI_PROVIDER_ERROR);
    }

    try {
      let models: any[] = [];

      switch (provider.type) {
        case 'GOOGLE':
          models = await this.fetchGoogleModels(apiKey);
          break;
        case 'OPENROUTER':
          models = await this.fetchOpenRouterModels(apiKey);
          break;
        case 'GROQ':
          models = await this.fetchGroqModels(apiKey);
          break;
        case 'MISTRAL':
          models = await this.fetchMistralModels(apiKey);
          break;
        case 'CUSTOM':
          models = await this.fetchCustomProviderModels(provider, apiKey);
          break;
        default:
          throw createError('Unsupported provider type', 400, ERROR_CODES.AI_PROVIDER_ERROR);
      }

      // Update models in database
      await this.updateProviderModels(providerId, models);

      return await prisma.aIModel.findMany({
        where: { providerId, isActive: true }
      });
    } catch (error) {
      logger.error(`Failed to fetch models for provider ${provider.name}:`, error);
      throw createError(
        `Failed to fetch models: ${(error as Error).message}`,
        500,
        ERROR_CODES.AI_PROVIDER_ERROR
      );
    }
  }

  // Get active API key for provider
  private async getActiveApiKey(providerId: string): Promise<string | null> {
    const apiKey = await prisma.aIProviderApiKey.findFirst({
      where: {
        providerId,
        isActive: true
      },
      orderBy: [
        { usageCount: 'asc' },
        { lastUsed: 'asc' }
      ]
    });

    if (!apiKey) return null;

    return this.decryptApiKey(apiKey.encryptedKey);
  }

  // Update API key usage
  private async updateApiKeyUsage(providerId: string, keyName: string): Promise<void> {
    await prisma.aIProviderApiKey.updateMany({
      where: {
        providerId,
        keyName
      },
      data: {
        lastUsed: new Date(),
        usageCount: {
          increment: 1
        }
      }
    });
  }

  // Fetch Google AI models
  private async fetchGoogleModels(apiKey: string): Promise<any[]> {
    const response = await axios.get(
      `${AI_PROVIDERS.GOOGLE.baseUrl}${AI_PROVIDERS.GOOGLE.modelsEndpoint}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      }
    );

    return response.data.models || [];
  }

  // Fetch OpenRouter models
  private async fetchOpenRouterModels(apiKey: string): Promise<any[]> {
    const response = await axios.get(
      `${AI_PROVIDERS.OPENROUTER.baseUrl}${AI_PROVIDERS.OPENROUTER.modelsEndpoint}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': process.env.FRONTEND_URL || 'http://localhost:3000',
          'X-Title': 'Piknowkyo Generator'
        }
      }
    );

    return response.data.data || [];
  }

  // Fetch Groq models
  private async fetchGroqModels(apiKey: string): Promise<any[]> {
    const response = await axios.get(
      `${AI_PROVIDERS.GROQ.baseUrl}${AI_PROVIDERS.GROQ.modelsEndpoint}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      }
    );

    return response.data.data || [];
  }

  // Fetch Mistral models
  private async fetchMistralModels(apiKey: string): Promise<any[]> {
    const response = await axios.get(
      `${AI_PROVIDERS.MISTRAL.baseUrl}${AI_PROVIDERS.MISTRAL.modelsEndpoint}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      }
    );

    return response.data.data || [];
  }

  // Fetch custom provider models
  private async fetchCustomProviderModels(provider: AIProvider, apiKey: string): Promise<any[]> {
    const config = provider.config ? JSON.parse(provider.config) : {};
    if (!config?.modelsEndpoint) {
      throw createError('Models endpoint not configured for custom provider', 400, ERROR_CODES.AI_PROVIDER_ERROR);
    }

    const response = await axios.get(
      `${provider.baseUrl}${config.modelsEndpoint}`,
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          ...config.headers
        }
      }
    );

    return response.data.data || response.data.models || [];
  }

  // Update provider models in database
  private async updateProviderModels(providerId: string, models: any[]): Promise<void> {
    // Deactivate existing models
    await prisma.aIModel.updateMany({
      where: { providerId },
      data: { isActive: false }
    });

    // Upsert new models
    for (const model of models) {
      await prisma.aIModel.upsert({
        where: {
          providerId_name: {
            providerId,
            name: model.id || model.name
          }
        },
        create: {
          providerId,
          name: model.id || model.name,
          displayName: model.name || model.display_name,
          description: model.description,
          maxTokens: model.context_length || model.max_tokens,
          isActive: true
        },
        update: {
          displayName: model.name || model.display_name,
          description: model.description,
          maxTokens: model.context_length || model.max_tokens,
          isActive: true,
          updatedAt: new Date()
        }
      });
    }
  }

  // Send chat completion request
  async sendChatCompletion(
    providerId: string,
    request: ChatCompletionRequest
  ): Promise<ChatCompletionResponse> {
    const provider = await this.getProvider(providerId);
    if (!provider) {
      throw createError('Provider not found', 404, ERROR_CODES.NOT_FOUND);
    }

    const apiKey = await this.getActiveApiKey(providerId);
    if (!apiKey) {
      throw createError('No active API key found for provider', 400, ERROR_CODES.AI_PROVIDER_ERROR);
    }

    try {
      let response: AxiosResponse;

      switch (provider.type) {
        case 'GOOGLE':
          response = await this.sendGoogleRequest(apiKey, request);
          break;
        case 'OPENROUTER':
        case 'GROQ':
        case 'MISTRAL':
          response = await this.sendOpenAICompatibleRequest(provider, apiKey, request);
          break;
        case 'CUSTOM':
          response = await this.sendCustomProviderRequest(provider, apiKey, request);
          break;
        default:
          throw createError('Unsupported provider type', 400, ERROR_CODES.AI_PROVIDER_ERROR);
      }

      // Update API key usage
      await this.updateApiKeyUsage(providerId, 'key_1'); // Simplified for now

      return response.data;
    } catch (error) {
      logger.error(`AI provider request failed for ${provider.name}:`, error);
      throw createError(
        `AI provider request failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        500,
        ERROR_CODES.AI_PROVIDER_ERROR
      );
    }
  }

  // Send Google AI request
  private async sendGoogleRequest(
    apiKey: string,
    request: ChatCompletionRequest
  ): Promise<AxiosResponse> {
    const url = `${AI_PROVIDERS.GOOGLE.baseUrl}/v1/models/${request.model}:generateContent`;
    
    // Convert OpenAI format to Google format
    const googleRequest = {
      contents: request.messages.map(msg => ({
        role: msg.role === 'assistant' ? 'model' : msg.role,
        parts: [{ text: msg.content }]
      })),
      generationConfig: {
        temperature: request.temperature,
        maxOutputTokens: request.max_tokens
      }
    };

    return await axios.post(url, googleRequest, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }

  // Send OpenAI-compatible request
  private async sendOpenAICompatibleRequest(
    provider: AIProvider,
    apiKey: string,
    request: ChatCompletionRequest
  ): Promise<AxiosResponse> {
    const baseUrl = provider.type === 'OPENROUTER' ? AI_PROVIDERS.OPENROUTER.baseUrl :
                   provider.type === 'GROQ' ? AI_PROVIDERS.GROQ.baseUrl :
                   AI_PROVIDERS.MISTRAL.baseUrl;

    const url = `${baseUrl}/v1/chat/completions`;

    const headers: Record<string, string> = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    };

    if (provider.type === 'OPENROUTER') {
      headers['HTTP-Referer'] = process.env.FRONTEND_URL || 'http://localhost:3000';
      headers['X-Title'] = 'Piknowkyo Generator';
    }

    return await axios.post(url, request, { headers });
  }

  // Send custom provider request
  private async sendCustomProviderRequest(
    provider: AIProvider,
    apiKey: string,
    request: ChatCompletionRequest
  ): Promise<AxiosResponse> {
    const config = provider.config ? JSON.parse(provider.config) : {};
    if (!config?.chatEndpoint) {
      throw createError('Chat endpoint not configured for custom provider', 400, ERROR_CODES.AI_PROVIDER_ERROR);
    }

    const url = `${provider.baseUrl}${config.chatEndpoint}`;

    return await axios.post(url, request, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        ...config.headers
      }
    });
  }
}
