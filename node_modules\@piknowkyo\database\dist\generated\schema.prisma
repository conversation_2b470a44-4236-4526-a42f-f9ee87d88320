// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../packages/database/src/generated"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  role      String   @default("USER")
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  videoProjects VideoProject[]
  userSettings  UserSettings?
  apiUsage      ApiUsage[]

  @@map("users")
}

// Video Project Management
model VideoProject {
  id              String   @id @default(cuid())
  title           String
  description     String?
  primaryLanguage String   @default("ENGLISH")
  status          String   @default("IDEA_GENERATED")
  metadata        String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user               User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId             String
  multilingualVideos MultilingualVideo[]
  comments           Comment[]

  @@map("video_projects")
}

model MultilingualVideo {
  id           String   @id @default(cuid())
  language     String
  title        String
  description  String?
  script       String?
  audioUrl     String?
  videoUrl     String?
  thumbnailUrl String?
  status       String   @default("SCRIPT_CREATED")
  metadata     String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  videoProject   VideoProject @relation(fields: [videoProjectId], references: [id], onDelete: Cascade)
  videoProjectId String

  @@map("multilingual_videos")
}

// AI Provider Management
model AIProvider {
  id        String   @id @default(cuid())
  name      String   @unique
  type      String
  baseUrl   String?
  isActive  Boolean  @default(true)
  isCustom  Boolean  @default(false)
  config    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  apiKeys      AIProviderApiKey[]
  models       AIModel[]
  userSettings UserAISettings[]

  @@map("ai_providers")
}

model AIProviderApiKey {
  id           String    @id @default(cuid())
  keyName      String // e.g., "primary", "secondary", "tertiary"
  encryptedKey String
  isActive     Boolean   @default(true)
  lastUsed     DateTime?
  usageCount   Int       @default(0)
  createdAt    DateTime  @default(now())

  // Relations
  provider   AIProvider @relation(fields: [providerId], references: [id], onDelete: Cascade)
  providerId String

  @@unique([providerId, keyName])
  @@map("ai_provider_api_keys")
}

model AIModel {
  id          String   @id @default(cuid())
  name        String
  displayName String?
  description String?
  maxTokens   Int?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  provider   AIProvider @relation(fields: [providerId], references: [id], onDelete: Cascade)
  providerId String

  @@unique([providerId, name])
  @@map("ai_models")
}

// Voice/TTS Provider Management
model TTSProvider {
  id        String   @id @default(cuid())
  name      String   @unique
  type      String
  baseUrl   String?
  isActive  Boolean  @default(true)
  isCustom  Boolean  @default(false)
  config    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  apiKeys      TTSProviderApiKey[]
  voices       Voice[]
  userSettings UserVoiceSettings[]

  @@map("tts_providers")
}

model TTSProviderApiKey {
  id           String    @id @default(cuid())
  keyName      String
  encryptedKey String
  isActive     Boolean   @default(true)
  lastUsed     DateTime?
  usageCount   Int       @default(0)
  createdAt    DateTime  @default(now())

  // Relations
  provider   TTSProvider @relation(fields: [providerId], references: [id], onDelete: Cascade)
  providerId String

  @@unique([providerId, keyName])
  @@map("tts_provider_api_keys")
}

model Voice {
  id         String   @id @default(cuid())
  voiceId    String // Provider's voice ID
  name       String
  language   String
  gender     String
  style      String?
  previewUrl String?
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  provider   TTSProvider @relation(fields: [providerId], references: [id], onDelete: Cascade)
  providerId String

  @@unique([providerId, voiceId])
  @@map("voices")
}

// User Settings and Preferences
model UserSettings {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user            User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId          String               @unique
  aiSettings      UserAISettings[]
  voiceSettings   UserVoiceSettings[]
  generalSettings UserGeneralSettings?

  @@map("user_settings")
}

model UserAISettings {
  id           String  @id @default(cuid())
  taskType     String // e.g., "script_generation", "translation", "metadata"
  temperature  Float   @default(0.7)
  maxTokens    Int     @default(2048)
  customPrompt String?
  isActive     Boolean @default(true)

  // Relations
  userSettings   UserSettings @relation(fields: [userSettingsId], references: [id], onDelete: Cascade)
  userSettingsId String
  aiProvider     AIProvider   @relation(fields: [aiProviderId], references: [id])
  aiProviderId   String
  modelName      String

  @@unique([userSettingsId, taskType])
  @@map("user_ai_settings")
}

model UserVoiceSettings {
  id       String  @id @default(cuid())
  language String
  speed    Float   @default(1.0)
  pitch    Float   @default(0.0)
  volume   Float   @default(0.0)
  isActive Boolean @default(true)

  // Relations
  userSettings   UserSettings @relation(fields: [userSettingsId], references: [id], onDelete: Cascade)
  userSettingsId String
  ttsProvider    TTSProvider  @relation(fields: [ttsProviderId], references: [id])
  ttsProviderId  String
  voiceId        String

  @@unique([userSettingsId, language])
  @@map("user_voice_settings")
}

model UserGeneralSettings {
  id                   String  @id @default(cuid())
  defaultLanguage      String  @default("ENGLISH")
  enableAutoCreation   Boolean @default(false)
  creationSchedule     String? // Cron expression
  maxVideosPerDay      Int     @default(10)
  enableNotifications  Boolean @default(true)
  notificationChannels String? // email, push, slack, etc. (JSON string)
  contentOutputDir     String  @default("./storage")
  defaultVideoDuration Int     @default(60)

  // Relations
  userSettings   UserSettings @relation(fields: [userSettingsId], references: [id], onDelete: Cascade)
  userSettingsId String       @unique

  @@map("user_general_settings")
}

// Comment Management
model Comment {
  id        String   @id @default(cuid())
  platform  String // youtube, tiktok, instagram
  content   String
  author    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  videoProject   VideoProject      @relation(fields: [videoProjectId], references: [id], onDelete: Cascade)
  videoProjectId String
  responses      CommentResponse[]

  @@map("comments")
}

model CommentResponse {
  id        String   @id @default(cuid())
  content   String
  status    String   @default("DRAFT")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  comment   Comment @relation(fields: [commentId], references: [id], onDelete: Cascade)
  commentId String

  @@map("comment_responses")
}

// API Usage Tracking
model ApiUsage {
  id           String   @id @default(cuid())
  provider     String
  model        String?
  endpoint     String
  tokens       Int?
  cost         Float?
  success      Boolean
  errorMessage String?
  createdAt    DateTime @default(now())

  // Relations
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String

  @@map("api_usage")
}

// System Configuration
model SystemConfig {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("system_config")
}
