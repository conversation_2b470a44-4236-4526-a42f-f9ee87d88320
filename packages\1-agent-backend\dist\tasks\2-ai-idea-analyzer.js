"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiIdeaAnalyzer = exports.AIIdeaAnalyzer = void 0;
const ai_provider_1 = require("../core/ai-provider");
const database_handler_1 = require("../core/database-handler");
const logger_1 = require("../core/logger");
class AIIdeaAnalyzer {
    async analyzeVideoIdea(projectId) {
        try {
            logger_1.logger.info(`Analyzing idea for project ${projectId}...`);
            const project = await database_handler_1.databaseHandler.getVideoProject(projectId);
            if (!project) {
                throw new Error(`Project ${projectId} not found`);
            }
            const analysisPrompt = `
        You are an expert content strategist analyzing a video idea for short-form content.

        Video Title: ${project.title}
        Description: ${project.description}

        Analyze this video idea and provide a detailed analysis in JSON format:
        {
          "viabilityScore": 1-100,
          "engagementPotential": 1-100,
          "targetAudience": "detailed audience description",
          "contentStructure": ["intro", "main points", "conclusion"],
          "keyPoints": ["point 1", "point 2", "point 3"],
          "hooks": ["attention-grabbing opening lines"],
          "callToAction": "suggested call to action",
          "estimatedDuration": 30-90,
          "difficulty": "easy/medium/hard",
          "requiredAssets": ["images", "music", "text overlays"]
        }

        Consider:
        - Current trends and audience preferences
        - Engagement potential on TikTok, YouTube Shorts, Instagram Reels
        - Content structure for maximum retention
        - Visual and audio requirements
      `;
            const response = await ai_provider_1.aiProvider.generateText(analysisPrompt);
            let analysis;
            try {
                analysis = JSON.parse(response);
            }
            catch (parseError) {
                logger_1.logger.error(projectId, `Failed to parse idea analysis: ${parseError}`);
                // Fallback to basic analysis
                analysis = this.generateBasicAnalysis(project.title, project.description || '');
            }
            // Update project with analysis data
            await database_handler_1.databaseHandler.updateVideoProject(projectId, {
                description: `${project.description}\n\nAnalysis: Viability ${analysis.viabilityScore}/100, Engagement ${analysis.engagementPotential}/100`,
            });
            logger_1.logger.info(`Completed idea analysis for project ${projectId}`);
            return analysis;
        }
        catch (error) {
            logger_1.logger.error(projectId, `Idea analysis failed: ${error}`);
            throw error;
        }
    }
    async processProjectsNeedingAnalysis() {
        try {
            const projects = await database_handler_1.databaseHandler.getVideoProjects('IDEA_GENERATED');
            for (const project of projects) {
                try {
                    await this.analyzeVideoIdea(project.id);
                    // Update status to indicate analysis is complete
                    await database_handler_1.databaseHandler.updateVideoProjectStatus(project.id, 'SCRIPT_CREATED');
                }
                catch (error) {
                    logger_1.logger.error(project.id, `Failed to analyze project: ${error}`);
                    await database_handler_1.databaseHandler.updateVideoProjectStatus(project.id, 'ERROR', `Analysis failed: ${error}`);
                }
            }
        }
        catch (error) {
            logger_1.logger.error('', `Failed to process projects needing analysis: ${error}`);
            throw error;
        }
    }
    generateBasicAnalysis(title, description) {
        // Basic fallback analysis based on title and description
        const wordCount = (title + ' ' + description).split(' ').length;
        const hasEducational = /learn|how|tutorial|guide|explain/i.test(title + description);
        const hasEntertainment = /funny|fun|amazing|cool|wow/i.test(title + description);
        return {
            viabilityScore: Math.min(90, 60 + wordCount * 2),
            engagementPotential: hasEntertainment ? 85 : hasEducational ? 75 : 65,
            targetAudience: hasEducational ? "learners and professionals" : "general audience",
            contentStructure: ["hook", "main content", "conclusion"],
            keyPoints: [
                "Main topic introduction",
                "Key information or demonstration",
                "Summary and takeaway"
            ],
            hooks: [
                "Did you know...",
                "Here's something amazing...",
                "You won't believe this..."
            ],
            callToAction: "Like and follow for more content!",
            estimatedDuration: Math.min(90, Math.max(30, wordCount * 3)),
            difficulty: wordCount > 20 ? 'medium' : 'easy',
            requiredAssets: ["background music", "text overlays", "images"]
        };
    }
    async getAnalysisForProject(projectId) {
        try {
            // In a real implementation, you might store analysis results in the database
            // For now, we'll re-analyze on demand
            return await this.analyzeVideoIdea(projectId);
        }
        catch (error) {
            logger_1.logger.error(projectId, `Failed to get analysis for project: ${error}`);
            return null;
        }
    }
    async validateIdeaQuality(analysis) {
        // Quality thresholds
        const minViability = 60;
        const minEngagement = 50;
        return analysis.viabilityScore >= minViability &&
            analysis.engagementPotential >= minEngagement;
    }
    async suggestImprovements(analysis) {
        const suggestions = [];
        if (analysis.viabilityScore < 70) {
            suggestions.push("Consider focusing on a more specific niche or trending topic");
        }
        if (analysis.engagementPotential < 60) {
            suggestions.push("Add more engaging hooks or interactive elements");
        }
        if (analysis.estimatedDuration > 75) {
            suggestions.push("Consider shortening the content for better retention");
        }
        if (analysis.difficulty === 'hard') {
            suggestions.push("Simplify the concept for broader audience appeal");
        }
        return suggestions;
    }
}
exports.AIIdeaAnalyzer = AIIdeaAnalyzer;
exports.aiIdeaAnalyzer = new AIIdeaAnalyzer();
