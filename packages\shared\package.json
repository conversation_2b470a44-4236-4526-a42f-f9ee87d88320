{"name": "@piknowkyo/shared", "version": "1.0.0", "description": "Shared types, constants, and utilities for Piknowkyo Generator", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "test": "jest"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./types": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "./constants": {"types": "./dist/constants/index.d.ts", "default": "./dist/constants/index.js"}, "./utils": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}, "./validators": {"types": "./dist/validators/index.d.ts", "default": "./dist/validators/index.js"}}}