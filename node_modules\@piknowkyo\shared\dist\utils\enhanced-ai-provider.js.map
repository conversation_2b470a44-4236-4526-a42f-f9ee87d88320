{"version": 3, "file": "enhanced-ai-provider.js", "sourceRoot": "", "sources": ["../../src/utils/enhanced-ai-provider.ts"], "names": [], "mappings": ";;;AAAA,qCAAkC;AAkClC,MAAa,kBAAkB;IAI7B;QAHQ,cAAS,GAA0C,IAAI,GAAG,EAAE,CAAC;QAC7D,gBAAW,GAA8B,IAAI,GAAG,EAAE,CAAC;QAGzD,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACzB,oCAAoC;QACpC,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAgC;QAChD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QACtC,eAAM,CAAC,IAAI,CAAC,sBAAsB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkB;QACrC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAClC,eAAM,CAAC,IAAI,CAAC,wBAAwB,UAAU,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,UAAkB;QAClC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,MAAoB;QACxD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACvC,eAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAc,EACd,QAAiB,EACjB,OAKC;QAED,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAE7E,kCAAkC;YAClC,MAAM,UAAU,GAAG,OAAO,EAAE,UAAU,IAAI,UAAU,EAAE,UAAU,IAAI,SAAS,CAAC;YAC9E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEpD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,YAAY,UAAU,YAAY,CAAC,CAAC;YACtD,CAAC;YAED,kCAAkC;YAClC,mEAAmE;YACnE,MAAM,QAAQ,GAAe;gBAC3B,OAAO,EAAE,6BAA6B,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK;gBAClE,KAAK,EAAE;oBACL,YAAY,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC;oBAC/B,gBAAgB,EAAE,GAAG;oBACrB,WAAW,EAAE,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;iBACvC;gBACD,KAAK,EAAE,OAAO,EAAE,KAAK,IAAI,UAAU,EAAE,KAAK,IAAI,QAAQ,CAAC,YAAY;gBACnE,QAAQ,EAAE,QAAQ,CAAC,IAAI;aACxB,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,wBAAwB,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC;YACzE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,UAAkB;QACnC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YACpD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,KAAK,CAAC;YACf,CAAC;YAED,4BAA4B;YAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;YAClE,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAlGD,gDAkGC;AAED,4BAA4B;AACf,QAAA,kBAAkB,GAAG,IAAI,kBAAkB,EAAE,CAAC;AAC3D,kBAAe,0BAAkB,CAAC"}