import { AIProviderType } from '../types/ai-provider';
export interface EnhancedAIProviderConfig {
    id: string;
    name: string;
    type: AIProviderType;
    apiKey: string;
    baseUrl?: string;
    models: string[];
    defaultModel: string;
    isActive: boolean;
}
export interface TaskAIConfig {
    taskType: string;
    providerId: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
}
export interface AIResponse {
    content: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
    model: string;
    provider: string;
}
export declare class EnhancedAIProvider {
    private providers;
    private taskConfigs;
    constructor();
    private initializeProviders;
    addProvider(config: EnhancedAIProviderConfig): Promise<void>;
    removeProvider(providerId: string): Promise<void>;
    getProvider(providerId: string): Promise<EnhancedAIProviderConfig | undefined>;
    listProviders(): Promise<EnhancedAIProviderConfig[]>;
    setTaskConfig(taskType: string, config: TaskAIConfig): Promise<void>;
    getTaskConfig(taskType: string): Promise<TaskAIConfig | undefined>;
    generateText(prompt: string, taskType?: string, options?: {
        temperature?: number;
        maxTokens?: number;
        model?: string;
        providerId?: string;
    }): Promise<AIResponse>;
    testProvider(providerId: string): Promise<boolean>;
}
export declare const enhancedAIProvider: EnhancedAIProvider;
export default enhancedAIProvider;
//# sourceMappingURL=enhanced-ai-provider.d.ts.map