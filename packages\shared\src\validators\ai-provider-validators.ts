import { z } from 'zod';

export const createAIProviderSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  type: z.enum(['GOOGLE', 'OPENROUTER', 'GROQ', 'MISTRAL', 'CUSTOM']),
  apiKey: z.string().min(1, 'API key is required'),
  baseUrl: z.string().url('Invalid base URL').optional(),
  isActive: z.boolean().optional(),
});

export const updateAIProviderSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  apiKey: z.string().min(1, 'API key is required').optional(),
  baseUrl: z.string().url('Invalid base URL').optional(),
  isActive: z.boolean().optional(),
});

export const testAIProviderSchema = z.object({
  prompt: z.string().min(1, 'Test prompt is required').max(500, 'Prompt too long').optional(),
});

export type CreateAIProviderInput = z.infer<typeof createAIProviderSchema>;
export type UpdateAIProviderInput = z.infer<typeof updateAIProviderSchema>;
export type TestAIProviderInput = z.infer<typeof testAIProviderSchema>;
