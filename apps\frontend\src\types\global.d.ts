// Global type declarations for the frontend application
import { UserRole } from '@shared/types';

declare global {
  namespace NodeJS {
    interface Global {
      frontendTestUtils: {
        createMockUser: () => {
          id: string;
          email: string;
          name: string;
          role: UserRole;
          isActive: boolean;
          createdAt: Date;
          updatedAt: Date;
        };
        createMockAuthContext: (user?: any) => {
          user: any;
          isLoading: boolean;
          isAuthenticated: boolean;
          login: jest.Mock;
          register: jest.Mock;
          logout: jest.Mock;
          refreshToken: jest.Mock;
        };
      };
    }
  }

  var frontendTestUtils: {
    createMockUser: () => {
      id: string;
      email: string;
      name: string;
      role: UserRole;
      isActive: boolean;
      createdAt: Date;
      updatedAt: Date;
    };
    createMockAuthContext: (user?: any) => {
      user: any;
      isLoading: boolean;
      isAuthenticated: boolean;
      login: jest.Mock;
      register: jest.Mock;
      logout: jest.Mock;
      refreshToken: jest.<PERSON>ck;
    };
  };
}

export {};
