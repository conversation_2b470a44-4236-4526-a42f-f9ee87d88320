"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationsProvider = exports.NotificationsProvider = void 0;
const logger_1 = require("./logger");
class NotificationsProvider {
    constructor() {
        this.config = {
            email: { enabled: false },
            webhook: { enabled: false, url: '' },
            slack: { enabled: false, webhookUrl: '' },
            discord: { enabled: false, webhookUrl: '' }
        };
        this.initializeProvider();
    }
    initializeProvider() {
        logger_1.logger.info('Initializing Notifications Provider...');
        // Load configuration from environment variables
        this.config = {
            email: {
                enabled: process.env.EMAIL_NOTIFICATIONS_ENABLED === 'true',
                smtp: process.env.SMTP_HOST ? {
                    host: process.env.SMTP_HOST,
                    port: parseInt(process.env.SMTP_PORT || '587'),
                    secure: process.env.SMTP_SECURE === 'true',
                    auth: {
                        user: process.env.SMTP_USER || '',
                        pass: process.env.SMTP_PASS || ''
                    }
                } : undefined
            },
            webhook: {
                enabled: process.env.WEBHOOK_NOTIFICATIONS_ENABLED === 'true',
                url: process.env.WEBHOOK_URL || ''
            },
            slack: {
                enabled: process.env.SLACK_NOTIFICATIONS_ENABLED === 'true',
                webhookUrl: process.env.SLACK_WEBHOOK_URL || ''
            },
            discord: {
                enabled: process.env.DISCORD_NOTIFICATIONS_ENABLED === 'true',
                webhookUrl: process.env.DISCORD_WEBHOOK_URL || ''
            }
        };
    }
    async sendNotification(notification) {
        const fullNotification = {
            ...notification,
            id: this.generateId(),
            timestamp: new Date()
        };
        logger_1.logger.info(`Sending notification: ${fullNotification.title}`);
        try {
            // Send to all enabled channels
            const promises = [];
            if (this.config.email?.enabled) {
                promises.push(this.sendEmailNotification(fullNotification));
            }
            if (this.config.webhook?.enabled) {
                promises.push(this.sendWebhookNotification(fullNotification));
            }
            if (this.config.slack?.enabled) {
                promises.push(this.sendSlackNotification(fullNotification));
            }
            if (this.config.discord?.enabled) {
                promises.push(this.sendDiscordNotification(fullNotification));
            }
            await Promise.allSettled(promises);
        }
        catch (error) {
            logger_1.logger.error('Failed to send notification:', error);
        }
    }
    async sendEmailNotification(notification) {
        // Mock email sending - implement with nodemailer or similar
        logger_1.logger.info(`Email notification sent: ${notification.title}`);
    }
    async sendWebhookNotification(notification) {
        // Mock webhook sending - implement with axios or fetch
        logger_1.logger.info(`Webhook notification sent: ${notification.title}`);
    }
    async sendSlackNotification(notification) {
        // Mock Slack notification - implement with Slack webhook
        logger_1.logger.info(`Slack notification sent: ${notification.title}`);
    }
    async sendDiscordNotification(notification) {
        // Mock Discord notification - implement with Discord webhook
        logger_1.logger.info(`Discord notification sent: ${notification.title}`);
    }
    async sendProjectStatusUpdate(projectId, status, userId) {
        await this.sendNotification({
            type: 'info',
            title: 'Project Status Update',
            message: `Project ${projectId} status changed to ${status}`,
            data: { projectId, status },
            userId
        });
    }
    async sendErrorNotification(error, context, userId) {
        await this.sendNotification({
            type: 'error',
            title: 'Error Occurred',
            message: `${context ? context + ': ' : ''}${error.message}`,
            data: { error: error.stack, context },
            userId
        });
    }
    async sendSuccessNotification(title, message, userId) {
        await this.sendNotification({
            type: 'success',
            title,
            message,
            userId
        });
    }
    generateId() {
        return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        logger_1.logger.info('Notifications configuration updated');
    }
    getConfig() {
        return { ...this.config };
    }
}
exports.NotificationsProvider = NotificationsProvider;
// Export singleton instance
exports.notificationsProvider = new NotificationsProvider();
exports.default = exports.notificationsProvider;
//# sourceMappingURL=notifications-provider.js.map