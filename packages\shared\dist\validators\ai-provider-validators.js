"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testAIProviderSchema = exports.updateAIProviderSchema = exports.createAIProviderSchema = void 0;
const zod_1 = require("zod");
exports.createAIProviderSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long'),
    type: zod_1.z.enum(['GOOGLE', 'OPENROUTER', 'GROQ', 'MISTRAL', 'CUSTOM']),
    apiKey: zod_1.z.string().min(1, 'API key is required'),
    baseUrl: zod_1.z.string().url('Invalid base URL').optional(),
    isActive: zod_1.z.boolean().optional(),
});
exports.updateAIProviderSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
    apiKey: zod_1.z.string().min(1, 'API key is required').optional(),
    baseUrl: zod_1.z.string().url('Invalid base URL').optional(),
    isActive: zod_1.z.boolean().optional(),
});
exports.testAIProviderSchema = zod_1.z.object({
    prompt: zod_1.z.string().min(1, 'Test prompt is required').max(500, 'Prompt too long').optional(),
});
//# sourceMappingURL=ai-provider-validators.js.map