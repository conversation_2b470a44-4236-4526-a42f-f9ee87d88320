"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dotenv = __importStar(require("dotenv"));
const auth_handler_1 = require("./core/auth-handler");
const task_manager_1 = require("./core/task-manager");
const ai_provider_1 = require("./core/ai-provider");
const database_handler_1 = require("./core/database-handler");
const notifications_provider_1 = require("./core/notifications-provider");
const tts_provider_1 = require("./core/tts-provider");
const image_provider_1 = require("./core/image-provider");
const _1_ai_trend_researcher_1 = require("./tasks/1-ai-trend-researcher");
const _2_ai_idea_analyzer_1 = require("./tasks/2-ai-idea-analyzer");
const _3_ai_script_writer_1 = require("./tasks/3-ai-script-writer");
const enhanced_ai_provider_1 = require("./core/enhanced-ai-provider");
const web_search_provider_1 = require("./core/web-search-provider");
const google_services_provider_1 = require("./core/google-services-provider");
const workflow_orchestrator_1 = require("./core/workflow-orchestrator");
const comment_management_system_1 = require("./core/comment-management-system");
const multilingual_video_service_1 = require("./core/multilingual-video-service");
dotenv.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
// Middleware to parse JSON bodies
app.use(express_1.default.json());
// Middleware for token verification
const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
    if (token == null)
        return res.sendStatus(401);
    try {
        const user = (0, auth_handler_1.verifyToken)(token);
        req.user = user;
        next();
    }
    catch (error) {
        return res.sendStatus(403);
    }
};
// Route for user registration
app.post('/register', async (req, res) => {
    try {
        const { username, email, password, role } = req.body;
        const user = await (0, auth_handler_1.registerUser)(username, email, password, role);
        res.status(201).json(user);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Route for user login
app.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;
        const { user, token } = await (0, auth_handler_1.loginUser)(email, password);
        res.json({ user, token });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Example protected route
app.get('/protected', authenticateToken, (req, res) => {
    res.json({ message: 'This is a protected route', user: req.user });
});
// Route to create a new task
app.post('/tasks', authenticateToken, async (req, res) => {
    try {
        const { name, description, priority, dependencies } = req.body;
        const task = await task_manager_1.taskManager.createTask(name, description, priority, dependencies);
        res.status(201).json(task);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Route to get a task by ID
app.get('/tasks/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const task = await task_manager_1.taskManager.getTask(id);
        if (task) {
            res.json(task);
        }
        else {
            res.status(404).json({ error: 'Task not found' });
        }
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// Route to update task status
app.put('/tasks/:id/status', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { status, error } = req.body;
        const updatedTask = await task_manager_1.taskManager.updateTaskStatus(id, status, error);
        if (updatedTask) {
            res.json(updatedTask);
        }
        else {
            res.status(404).json({ error: 'Task not found' });
        }
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Route to get all tasks
app.get('/tasks', authenticateToken, async (req, res) => {
    try {
        const { status, priority } = req.query;
        const tasks = await task_manager_1.taskManager.getTasks(status, priority);
        res.json(tasks);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// Route to process AI task
app.post('/ai/generate-text', authenticateToken, async (req, res) => {
    try {
        const { prompt } = req.body;
        const generatedText = await ai_provider_1.aiProvider.generateText(prompt);
        res.json({ generatedText });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// Video Project Management Routes
app.post('/video-projects', authenticateToken, async (req, res) => {
    try {
        const { title, description, aiConfigurationId, primaryLanguage, createMultilingual } = req.body;
        // Create the main video project with multilingual fields
        const project = await database_handler_1.databaseHandler.createVideoProject(title, description, aiConfigurationId);
        // Update with multilingual fields if provided
        if (primaryLanguage) {
            try {
                const updatedProject = await database_handler_1.databaseHandler.updateVideoProject(project.id, {
                    primaryLanguage,
                    originalTitle: title,
                    originalDescription: description
                });
                // Automatically create multilingual versions if requested
                if (createMultilingual) {
                    try {
                        const targetLanguages = ['FRENCH', 'ENGLISH', 'SPANISH'].filter(lang => lang !== primaryLanguage);
                        const createdVersions = await multilingual_video_service_1.multilingualVideoService.createMultilingualVersions({
                            videoProjectId: project.id,
                            targetLanguages: targetLanguages
                        });
                        res.status(201).json({
                            ...updatedProject,
                            multilingualVersionsCreated: createdVersions.length,
                            targetLanguages
                        });
                    }
                    catch (multilingualError) {
                        console.warn('Failed to create multilingual versions:', multilingualError.message);
                        res.status(201).json({
                            ...updatedProject,
                            multilingualWarning: 'Project created but multilingual versions failed: ' + multilingualError.message
                        });
                    }
                }
                else {
                    res.status(201).json(updatedProject);
                }
            }
            catch (updateError) {
                console.warn('Failed to update project with multilingual fields:', updateError.message);
                res.status(201).json({
                    ...project,
                    warning: 'Project created but multilingual fields update failed: ' + updateError.message
                });
            }
        }
        else {
            res.status(201).json(project);
        }
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/video-projects', authenticateToken, async (req, res) => {
    try {
        const { status } = req.query;
        const projects = await database_handler_1.databaseHandler.getVideoProjects(status);
        res.json(projects);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.get('/video-projects/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const project = await database_handler_1.databaseHandler.getVideoProject(id);
        if (project) {
            res.json(project);
        }
        else {
            res.status(404).json({ error: 'Video project not found' });
        }
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.put('/video-projects/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const project = await database_handler_1.databaseHandler.updateVideoProject(id, updateData);
        res.json(project);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.put('/video-projects/:id/status', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { status, errorMessage } = req.body;
        const project = await database_handler_1.databaseHandler.updateVideoProjectStatus(id, status, errorMessage);
        // Send notification for status change
        await notifications_provider_1.notificationsProvider.notifyVideoProjectStatusChange(id, status, project.title);
        res.json(project);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// AI Configuration Management Routes
app.post('/ai-configurations', authenticateToken, async (req, res) => {
    try {
        const { name, prompt, persona, settings } = req.body;
        const config = await database_handler_1.databaseHandler.createAIConfiguration(name, prompt, persona, settings);
        res.status(201).json(config);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/ai-configurations', authenticateToken, async (req, res) => {
    try {
        const configurations = await database_handler_1.databaseHandler.getAIConfigurations();
        res.json(configurations);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.get('/ai-configurations/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const config = await database_handler_1.databaseHandler.getAIConfiguration(id);
        if (config) {
            res.json(config);
        }
        else {
            res.status(404).json({ error: 'AI configuration not found' });
        }
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.put('/ai-configurations/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const config = await database_handler_1.databaseHandler.updateAIConfiguration(id, updateData);
        res.json(config);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.delete('/ai-configurations/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        await database_handler_1.databaseHandler.deleteAIConfiguration(id);
        res.status(204).send();
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// TTS Routes
app.post('/tts/generate', authenticateToken, async (req, res) => {
    try {
        const { text, options } = req.body;
        const result = await tts_provider_1.ttsProvider.generateSpeech(text, options);
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.get('/tts/voices', authenticateToken, async (req, res) => {
    try {
        const voices = await tts_provider_1.ttsProvider.getAvailableVoices();
        res.json({ voices });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/tts/test', authenticateToken, async (req, res) => {
    try {
        const result = await tts_provider_1.ttsProvider.testTTS();
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// Image Generation Routes
app.post('/images/generate', authenticateToken, async (req, res) => {
    try {
        const { prompt, options } = req.body;
        const result = await image_provider_1.imageProvider.generateImage(prompt, options);
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/images/thumbnail', authenticateToken, async (req, res) => {
    try {
        const { prompt } = req.body;
        const result = await image_provider_1.imageProvider.generateThumbnail(prompt);
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.get('/images/styles', authenticateToken, async (req, res) => {
    try {
        const styles = image_provider_1.imageProvider.getAvailableStyles();
        res.json({ styles });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/images/test', authenticateToken, async (req, res) => {
    try {
        const result = await image_provider_1.imageProvider.testImageGeneration();
        res.json(result);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// Comment Management Routes
app.post('/comments', authenticateToken, async (req, res) => {
    try {
        const { platform, commentId, text, author, videoProjectId } = req.body;
        const comment = await database_handler_1.databaseHandler.createUserComment(platform, commentId, text, author, videoProjectId);
        res.status(201).json(comment);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/comments/:id/response-draft', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { draftText } = req.body;
        const draft = await database_handler_1.databaseHandler.createAIResponseDraft(id, draftText);
        res.status(201).json(draft);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/response-drafts/pending', authenticateToken, async (req, res) => {
    try {
        const drafts = await database_handler_1.databaseHandler.getPendingResponseDrafts();
        res.json(drafts);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.put('/response-drafts/:id/status', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        const draft = await database_handler_1.databaseHandler.updateResponseDraftStatus(id, status);
        res.json(draft);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Notification Routes
app.post('/notifications/test', authenticateToken, async (req, res) => {
    try {
        const results = await notifications_provider_1.notificationsProvider.testNotifications();
        res.json(results);
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// Content Creation Pipeline Routes
app.post('/pipeline/research-trends', authenticateToken, async (req, res) => {
    try {
        const { maxProjects } = req.body;
        const projectIds = await _1_ai_trend_researcher_1.aiTrendResearcher.processTopTrends(maxProjects || 3);
        res.json({ projectIds, count: projectIds.length });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.get('/pipeline/trends', authenticateToken, async (req, res) => {
    try {
        const trends = await _1_ai_trend_researcher_1.aiTrendResearcher.researchTrends();
        res.json({ trends });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/pipeline/analyze-idea/:projectId', authenticateToken, async (req, res) => {
    try {
        const { projectId } = req.params;
        const analysis = await _2_ai_idea_analyzer_1.aiIdeaAnalyzer.analyzeVideoIdea(projectId);
        res.json({ analysis });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/pipeline/generate-script/:projectId', authenticateToken, async (req, res) => {
    try {
        const { projectId } = req.params;
        const script = await _3_ai_script_writer_1.aiScriptWriter.generateScript(projectId);
        res.json({ script });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/pipeline/process-analysis', authenticateToken, async (req, res) => {
    try {
        await _2_ai_idea_analyzer_1.aiIdeaAnalyzer.processProjectsNeedingAnalysis();
        res.json({ message: 'Analysis processing completed' });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/pipeline/process-scripts', authenticateToken, async (req, res) => {
    try {
        await _3_ai_script_writer_1.aiScriptWriter.processProjectsNeedingScripts();
        res.json({ message: 'Script processing completed' });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/pipeline/validate-script/:projectId', authenticateToken, async (req, res) => {
    try {
        const { projectId } = req.params;
        const script = await _3_ai_script_writer_1.aiScriptWriter.generateScript(projectId);
        const validation = await _3_ai_script_writer_1.aiScriptWriter.validateScript(script);
        res.json({ validation, script });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Enhanced AI Provider Routes
app.get('/ai/providers', authenticateToken, async (req, res) => {
    try {
        const providers = enhanced_ai_provider_1.enhancedAIProvider.getProviders();
        res.json({ providers });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/ai/test-provider', authenticateToken, async (req, res) => {
    try {
        const { providerId, model } = req.body;
        const result = await enhanced_ai_provider_1.enhancedAIProvider.testProvider(providerId, model);
        res.json(result);
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/ai/generate', authenticateToken, async (req, res) => {
    try {
        const { prompt, taskType, useWebSearch } = req.body;
        const response = useWebSearch
            ? await enhanced_ai_provider_1.enhancedAIProvider.generateWithWebSearch(prompt, taskType)
            : await enhanced_ai_provider_1.enhancedAIProvider.generateText(prompt, taskType);
        res.json({ response });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Web Search Routes
app.get('/search/trends', authenticateToken, async (req, res) => {
    try {
        const { query, region } = req.query;
        const trends = await web_search_provider_1.webSearchProvider.searchTrends(query, region);
        res.json({ trends });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.get('/search/social/:platform', authenticateToken, async (req, res) => {
    try {
        const { platform } = req.params;
        const { query } = req.query;
        const results = await web_search_provider_1.webSearchProvider.searchSocialMedia(platform, query);
        res.json({ results });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/search/youtube', authenticateToken, async (req, res) => {
    try {
        const { query, maxResults } = req.query;
        const results = await web_search_provider_1.webSearchProvider.searchYouTube(query, parseInt(maxResults) || 10);
        res.json({ results });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/search/reddit', authenticateToken, async (req, res) => {
    try {
        const { query, subreddit } = req.query;
        const results = await web_search_provider_1.webSearchProvider.searchReddit(query, subreddit);
        res.json({ results });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/search/twitter', authenticateToken, async (req, res) => {
    try {
        const { query } = req.query;
        const results = await web_search_provider_1.webSearchProvider.searchTwitter(query);
        res.json({ results });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Google Services Routes
app.post('/google/tts', authenticateToken, async (req, res) => {
    try {
        const { text, options } = req.body;
        const result = await google_services_provider_1.googleServicesProvider.generateSpeech(text, options);
        res.json({ result });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/google/image', authenticateToken, async (req, res) => {
    try {
        const { prompt, options } = req.body;
        const result = await google_services_provider_1.googleServicesProvider.generateImage(prompt, options);
        res.json({ result });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/google/music', authenticateToken, async (req, res) => {
    try {
        const { description, options } = req.body;
        const result = await google_services_provider_1.googleServicesProvider.generateMusic(description, options);
        res.json({ result });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/google/voices', authenticateToken, async (req, res) => {
    try {
        const { languageCode } = req.query;
        const voices = await google_services_provider_1.googleServicesProvider.getAvailableVoices(languageCode);
        res.json({ voices });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// Workflow Orchestrator Routes
app.get('/workflows', authenticateToken, async (req, res) => {
    try {
        const workflows = workflow_orchestrator_1.workflowOrchestrator.getWorkflows();
        res.json({ workflows });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/workflows/:workflowId/execute', authenticateToken, async (req, res) => {
    try {
        const { workflowId } = req.params;
        const executionId = await workflow_orchestrator_1.workflowOrchestrator.executeWorkflow(workflowId, true);
        res.json({ executionId, message: 'Workflow execution started' });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/workflows/executions', authenticateToken, async (req, res) => {
    try {
        const executions = workflow_orchestrator_1.workflowOrchestrator.getActiveExecutions();
        res.json({ executions });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/workflows/executions/:executionId/stop', authenticateToken, async (req, res) => {
    try {
        const { executionId } = req.params;
        await workflow_orchestrator_1.workflowOrchestrator.stopWorkflow(executionId);
        res.json({ message: 'Workflow execution stopped' });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.put('/workflows/:workflowId', authenticateToken, async (req, res) => {
    try {
        const { workflowId } = req.params;
        const config = req.body;
        await workflow_orchestrator_1.workflowOrchestrator.updateWorkflowConfig(workflowId, config);
        res.json({ message: 'Workflow configuration updated' });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
// Comment Management Routes
app.get('/comments/project/:projectId', authenticateToken, async (req, res) => {
    try {
        const { projectId } = req.params;
        const comments = await comment_management_system_1.commentManagementSystem.getCommentsByProject(projectId);
        res.json({ comments });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/comments/analyze', authenticateToken, async (req, res) => {
    try {
        const { comment } = req.body;
        const analysis = await comment_management_system_1.commentManagementSystem.analyzeComment(comment);
        res.json({ analysis });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/comments/generate-response', authenticateToken, async (req, res) => {
    try {
        const { comment, analysis, customTone } = req.body;
        const response = await comment_management_system_1.commentManagementSystem.generateResponse(comment, analysis, customTone);
        res.json({ response });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/comments/process/:projectId?', authenticateToken, async (req, res) => {
    try {
        const { projectId } = req.params;
        await comment_management_system_1.commentManagementSystem.processNewComments(projectId);
        res.json({ message: 'Comments processed successfully' });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/comments/responses/pending', authenticateToken, async (req, res) => {
    try {
        const responses = await comment_management_system_1.commentManagementSystem.getPendingResponses();
        res.json({ responses });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
app.post('/comments/responses/:responseId/approve', authenticateToken, async (req, res) => {
    try {
        const { responseId } = req.params;
        const { approvedBy } = req.body;
        await comment_management_system_1.commentManagementSystem.approveResponse(responseId, approvedBy);
        res.json({ message: 'Response approved' });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.post('/comments/responses/:responseId/publish', authenticateToken, async (req, res) => {
    try {
        const { responseId } = req.params;
        await comment_management_system_1.commentManagementSystem.publishResponse(responseId);
        res.json({ message: 'Response published' });
    }
    catch (error) {
        res.status(400).json({ error: error.message });
    }
});
app.get('/comments/:commentId/responses', authenticateToken, async (req, res) => {
    try {
        const { commentId } = req.params;
        const responses = await comment_management_system_1.commentManagementSystem.getResponsesByComment(commentId);
        res.json({ responses });
    }
    catch (error) {
        res.status(500).json({ error: error.message });
    }
});
// ===== MULTILINGUAL VIDEO ENDPOINTS =====
// Create multilingual versions of a video project
app.post('/video-projects/:id/multilingual', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { targetLanguages, customPrompts } = req.body;
        if (!targetLanguages || !Array.isArray(targetLanguages)) {
            return res.status(400).json({ error: 'targetLanguages array is required' });
        }
        const createdVersions = await multilingual_video_service_1.multilingualVideoService.createMultilingualVersions({
            videoProjectId: id,
            targetLanguages,
            customPrompts
        });
        res.json({
            success: true,
            message: `Created ${createdVersions.length} multilingual versions`,
            createdVersions
        });
    }
    catch (error) {
        console.error('Error creating multilingual versions:', error);
        res.status(500).json({ error: error.message });
    }
});
// Get all multilingual versions of a video project
app.get('/video-projects/:id/multilingual', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const versions = await multilingual_video_service_1.multilingualVideoService.getMultilingualVersions(id);
        res.json({ versions });
    }
    catch (error) {
        console.error('Error getting multilingual versions:', error);
        res.status(500).json({ error: error.message });
    }
});
// Update a specific multilingual version
app.put('/multilingual-videos/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const updatedVersion = await multilingual_video_service_1.multilingualVideoService.updateMultilingualVersion(id, updateData);
        res.json({
            success: true,
            message: 'Multilingual version updated successfully',
            version: updatedVersion
        });
    }
    catch (error) {
        console.error('Error updating multilingual version:', error);
        res.status(500).json({ error: error.message });
    }
});
// Delete a specific multilingual version
app.delete('/multilingual-videos/:id', authenticateToken, async (req, res) => {
    try {
        const { id } = req.params;
        await multilingual_video_service_1.multilingualVideoService.deleteMultilingualVersion(id);
        res.json({
            success: true,
            message: 'Multilingual version deleted successfully'
        });
    }
    catch (error) {
        console.error('Error deleting multilingual version:', error);
        res.status(500).json({ error: error.message });
    }
});
// Get supported languages
app.get('/multilingual/languages', authenticateToken, async (req, res) => {
    try {
        const languages = multilingual_video_service_1.multilingualVideoService.getSupportedLanguages();
        const languageConfigs = languages.map(lang => ({
            code: lang,
            config: multilingual_video_service_1.multilingualVideoService.getLanguageConfig(lang)
        }));
        res.json({ languages: languageConfigs });
    }
    catch (error) {
        console.error('Error getting supported languages:', error);
        res.status(500).json({ error: error.message });
    }
});
// Health Check Route
app.get('/health', async (req, res) => {
    try {
        const dbHealth = await database_handler_1.databaseHandler.healthCheck();
        res.json({
            status: 'ok',
            database: dbHealth ? 'connected' : 'disconnected',
            timestamp: new Date().toISOString(),
        });
    }
    catch (error) {
        res.status(500).json({
            status: 'error',
            error: error.message,
            timestamp: new Date().toISOString(),
        });
    }
});
// Start the server
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
});
exports.default = app;
